name: com.sbpay
description: "SB-Pay Flutter - SMS Transaction Monitoring App"
publish_to: 'none'

version: 2.0.1+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # UI & Navigation
  cupertino_icons: ^1.0.8
  
  # State Management
  provider: ^6.1.2
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  # SQLite Database
  sqflite: ^2.3.0
  path: ^1.8.3
  
  # Network
  dio: ^5.4.0
  connectivity_plus: ^6.0.3
  
  # Permissions
  permission_handler: ^11.3.0
  
  # Platform Channels & Device Info
  device_info_plus: ^10.1.0
  package_info_plus: ^8.0.0
  
  # JSON Serialization
  json_annotation: ^4.8.1
  
  # Forms & Validation
  # formz: ^0.7.0
  
  # Utilities
  equatable: ^2.0.5
  uuid: ^4.3.3
  country_code_picker: ^3.3.0
  flash: ^3.1.1
  pinput: ^5.0.1
  android_package_installer: ^0.0.3
  # change_app_package_name: ^1.5.0

  # File Sharing
  share_plus: ^7.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting
  flutter_lints: ^5.0.0
  
  # Code Generation
  build_runner: ^2.4.7
  hive_generator: ^2.0.1
  json_serializable: ^6.7.1

# Flutter configuration
flutter:
  uses-material-design: true
  
  # Assets
  assets:
    - assets/images/
    - assets/icons/
