package com.sbpay

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.util.Log
import androidx.core.content.FileProvider
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

class LoggerModule(private val context: Context) {
    companion object {
        private const val TAG = "LoggerModule"
        private const val LOGS_DIR = "logs"
        private const val LOG_FILE_PREFIX = "logs_"
        private const val LOG_FILE_EXTENSION = ".txt"
        private const val DATE_FORMAT = "yyyyMMdd"
        private const val TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss"
        private const val RETENTION_DAYS = 3
    }

    private val logsDirectory: File by lazy {
        File(context.filesDir, LOGS_DIR).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }

    private val dateFormat = SimpleDateFormat(DATE_FORMAT, Locale.getDefault())
    private val timestampFormat = SimpleDateFormat(TIMESTAMP_FORMAT, Locale.getDefault())

    /**
     * Write a log entry to the current date's log file
     */
    fun writeLog(tag: String, message: String): Boolean {
        return try {
            val currentDate = dateFormat.format(Date())
            val logFile = File(logsDirectory, "$LOG_FILE_PREFIX$currentDate$LOG_FILE_EXTENSION")
            val timestamp = timestampFormat.format(Date())
            
            val logEntry = buildString {
                append("====== $tag - $timestamp ======\n")
                append("$message\n")
                append("\n") // Blank line for readability
            }

            FileWriter(logFile, true).use { writer ->
                writer.append(logEntry)
                writer.flush()
            }

            Log.d(TAG, "Log written successfully: $tag")
            true
        } catch (e: IOException) {
            Log.e(TAG, "Failed to write log: ${e.message}")
            false
        }
    }

    /**
     * Clear log files older than the specified retention days
     * Current day's logs are never deleted
     */
    fun clearOldLogs(retentionDays: Int = RETENTION_DAYS): Boolean {
        return try {
            val currentDate = Calendar.getInstance()
            val cutoffDate = Calendar.getInstance().apply {
                add(Calendar.DAY_OF_YEAR, -retentionDays)
            }
            
            val currentDateStr = dateFormat.format(currentDate.time)
            var deletedCount = 0

            logsDirectory.listFiles()?.forEach { file ->
                if (file.name.startsWith(LOG_FILE_PREFIX) && file.name.endsWith(LOG_FILE_EXTENSION)) {
                    val dateStr = file.name.substring(
                        LOG_FILE_PREFIX.length,
                        file.name.length - LOG_FILE_EXTENSION.length
                    )
                    
                    // Never delete current day's logs
                    if (dateStr != currentDateStr) {
                        try {
                            val fileDate = dateFormat.parse(dateStr)
                            if (fileDate != null && fileDate.before(cutoffDate.time)) {
                                if (file.delete()) {
                                    deletedCount++
                                    Log.d(TAG, "Deleted old log file: ${file.name}")
                                }
                            }
                        } catch (e: Exception) {
                            Log.w(TAG, "Could not parse date from filename: ${file.name}")
                        }
                    }
                }
            }

            Log.d(TAG, "Cleared $deletedCount old log files")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear old logs: ${e.message}")
            false
        }
    }

    /**
     * Get the file path/URI of the latest log file for sharing
     */
    fun getLogFilePath(): String? {
        return try {
            val currentDate = dateFormat.format(Date())
            val logFile = File(logsDirectory, "$LOG_FILE_PREFIX$currentDate$LOG_FILE_EXTENSION")

            Log.d(TAG, "Checking log file: ${logFile.absolutePath}")
            Log.d(TAG, "File exists: ${logFile.exists()}, File size: ${logFile.length()}")

            // If file doesn't exist or is empty, create it with a basic entry
            if (!logFile.exists() || logFile.length() == 0L) {
                Log.d(TAG, "Creating log file with initial entry")
                writeLog("LOGGER", "Log file created for sharing")
            }

            if (logFile.exists() && logFile.length() > 0L) {
                // Use FileProvider to get a shareable URI
                val uri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    logFile
                )
                Log.d(TAG, "Generated URI: $uri")
                uri.toString()
            } else {
                Log.w(TAG, "No log file exists for current date or file is empty")
                Log.w(TAG, "Expected file path: ${logFile.absolutePath}")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get log file path: ${e.message}")
            e.printStackTrace()
            null
        }
    }

    fun getShareableLogFilePath(): String? {
        return try {
            val currentDate = dateFormat.format(Date())
            val logFile = File(logsDirectory, "$LOG_FILE_PREFIX$currentDate$LOG_FILE_EXTENSION")

            if (!logFile.exists() || logFile.length() == 0L) {
                writeLog("LOGGER", "Log file created for sharing")
            }

            if (logFile.exists() && logFile.length() > 0L) {
                // Copy to external cache directory for easier sharing
                val externalCacheDir = context.externalCacheDir
                if (externalCacheDir != null) {
                    val shareableFile = File(externalCacheDir, "logs_${currentDate}.txt")
                    logFile.copyTo(shareableFile, overwrite = true)
                    Log.d(TAG, "Copied log file to shareable location: ${shareableFile.absolutePath}")
                    return shareableFile.absolutePath
                } else {
                    // Fallback to FileProvider URI
                    val uri = FileProvider.getUriForFile(
                        context,
                        "${context.packageName}.fileprovider",
                        logFile
                    )
                    return uri.toString()
                }
            } else {
                Log.w(TAG, "No log file exists for sharing")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get shareable log file path: ${e.message}")
            e.printStackTrace()
            null
        }
    }

    /**
     * Get all available log files for sharing
     */
    fun getAllLogFiles(): List<String> {
        return try {
            val logFiles = mutableListOf<String>()
            logsDirectory.listFiles()?.forEach { file ->
                if (file.name.startsWith(LOG_FILE_PREFIX) && 
                    file.name.endsWith(LOG_FILE_EXTENSION) && 
                    file.length() > 0L) {
                    try {
                        val uri = FileProvider.getUriForFile(
                            context,
                            "${context.packageName}.fileprovider",
                            file
                        )
                        logFiles.add(uri.toString())
                    } catch (e: Exception) {
                        Log.w(TAG, "Could not create URI for file: ${file.name}")
                    }
                }
            }
            logFiles.sortedDescending() // Most recent first
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get all log files: ${e.message}")
            emptyList()
        }
    }

    /**
     * Log device boot detection
     */
    fun logDeviceBoot() {
        writeLog("DEVICE BOOT", "Device boot detected")
    }

    /**
     * Auto-cleanup old logs when writing new logs
     */
    private fun autoCleanup() {
        try {
            clearOldLogs()
        } catch (e: Exception) {
            Log.w(TAG, "Auto-cleanup failed: ${e.message}")
        }
    }

    /**
     * Initialize logger and perform auto-cleanup
     */
    fun initialize() {
        Log.d(TAG, "Initializing LoggerModule")
        autoCleanup()
        writeLog("LOGGER INIT", "Native logger initialized successfully")
    }
}
