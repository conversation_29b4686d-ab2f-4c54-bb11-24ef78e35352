package com.sbpay

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.telephony.SubscriptionManager
import android.telephony.TelephonyManager
import android.util.Log
import androidx.core.app.NotificationCompat
import com.tencent.mmkv.MMKV
import org.json.JSONArray
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.EventChannel

class SimStateMonitorModule(context: Context) {
    companion object {
        const val CHANNEL_ID = "sim_state_monitor"
        const val NOTIFICATION_ID = 1001
        const val SIM_STATE_KEY = "sim_state"
    }
    private val TAG = "SimStateMonitorModule"

    private val context: Context
    private lateinit var subscriptionManager: SubscriptionManager
    private lateinit var mmkv: MMKV
    private var previousSimState: String = ""

    init {
        this.context = context
        this.subscriptionManager = context.getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE) as SubscriptionManager
        this.mmkv = MMKV.defaultMMKV()!!
        this.previousSimState = mmkv.decodeString(SIM_STATE_KEY, "") ?: ""
    }

    fun startMonitoring(): Boolean {
        try {
            val intent = Intent(this.context, SimStateMonitorService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                this.context.startForegroundService(intent)
            } else {
                this.context.startService(intent)
            }
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start monitoring: ${e.message}")
            return false
        }
    }

    fun stopMonitoring(): Boolean {
        try {
            val intent = Intent(this.context, SimStateMonitorService::class.java)
            this.context.stopService(intent)
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop monitoring: ${e.message}")
            return false
        }
    }

    // Removed React Native sendEvent method - will use Flutter MethodChannel instead
}

class SimStateMonitorService : Service() {
    companion object {
        var eventsSink: EventChannel.EventSink? = null
        const val CHANNEL_ID = "sim_monitor_channel"
        const val NOTIFICATION_ID = 2
    }
    private val TAG = "SimStateMonitorService"
    private lateinit var subscriptionManager: SubscriptionManager
    private lateinit var mmkv: MMKV
    private var previousSimState: String = ""
    private var monitoringThread: Thread? = null
    private var isMonitoring = false

    override fun onCreate() {
        super.onCreate()
        subscriptionManager = getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE) as SubscriptionManager
        mmkv = MMKV.defaultMMKV()!!
        previousSimState = mmkv.decodeString(SimStateMonitorModule.SIM_STATE_KEY, "") ?: ""
        createNotificationChannel()
        startForeground(SimStateMonitorModule.NOTIFICATION_ID, createNotification())
        startMonitoring()
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Log.d(TAG, "Creating notification channel with ID: ${SimStateMonitorModule.CHANNEL_ID}")
            val channel = NotificationChannel(
                SimStateMonitorModule.CHANNEL_ID,
                "SIM State Monitor",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Monitors SIM card state changes"
            }
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            Log.d(TAG, "Notification channel created successfully")
        } else {
            Log.d(TAG, "Notification channels not supported on this Android version")
        }
    }

    private fun createNotification(): Notification {
        Log.d(TAG, "Creating notification with channel ID: ${SimStateMonitorModule.CHANNEL_ID}")
        val notification = NotificationCompat.Builder(this, SimStateMonitorModule.CHANNEL_ID)
            .setContentTitle("SIM Monitor Active")
            .setContentText("Monitoring SIM card changes")
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
        Log.d(TAG, "Notification created successfully")
        return notification
    }

    private fun getCurrentSimState(): String {
        val activeSubscriptionInfoList = subscriptionManager.activeSubscriptionInfoList
        val simStateArray = JSONArray()

        activeSubscriptionInfoList?.forEach { subscriptionInfo ->
            val simInfoObject = org.json.JSONObject().apply {
                put("slotIndex", subscriptionInfo.simSlotIndex)
                put("iccId", subscriptionInfo.iccId ?: "")
                put("subscriptionId", subscriptionInfo.subscriptionId)
            }
            simStateArray.put(simInfoObject)
        }

        return simStateArray.toString()
    }

    private fun checkSimStateAndNotify() {
        val currentSimState = getCurrentSimState()
        if (currentSimState != previousSimState) {
            // Get verified SIMs from MMKV storage
            val verifiedSimsJson = mmkv.decodeString("verified_sims", "[]")
            val verifiedSimsArray = try {
                JSONArray(verifiedSimsJson)
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing verified SIMs: ${e.message}")
                JSONArray()
            }

            // Parse current SIM state
            val currentSimArray = try {
                JSONArray(currentSimState)
            } catch (e: Exception) {
                JSONArray()
            }

            // Validate SIM changes
            var isValid = true
            if (verifiedSimsArray.length() > 0 && currentSimArray.length() > 0) {
                isValid = (0 until currentSimArray.length()).all { i ->
                    val currentSim = currentSimArray.getJSONObject(i)
                    var simVerified = false
                    for (j in 0 until verifiedSimsArray.length()) {
                        val verifiedSim = verifiedSimsArray.getJSONObject(j)
                        if (verifiedSim.getInt("slotIndex") == currentSim.getInt("slotIndex") &&
                            verifiedSim.getString("iccId") == currentSim.getString("iccId") &&
                            verifiedSim.getInt("subscriptionId") == currentSim.getInt("subscriptionId")) {
                            simVerified = true
                            break
                        }
                    }
                    simVerified
                }
            } else if (verifiedSimsArray.length() > 0 && currentSimArray.length() == 0) {
                // If we had verified SIMs before but now have none, it's invalid
                isValid = false
            }
            // If we had no verified SIMs before, we don't need to validate (isValid remains true)

            // Only show notification and stop service if SIM is actually invalid
            if (!isValid) {
                // Create notification for SIM verification
                val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                val notification = NotificationCompat.Builder(this, SimStateMonitorModule.CHANNEL_ID)
                    .setContentTitle("Unauthorized SIM Card Detected")
                    .setContentText("SMS service has been stopped due to unauthorized SIM card. Please verify your SIM card to continue.")
                    .setSmallIcon(android.R.drawable.ic_dialog_alert)
                    .setPriority(NotificationCompat.PRIORITY_HIGH)
                    .build()

                notificationManager.notify(SimStateMonitorModule.NOTIFICATION_ID + 1, notification)

                // Stop SMS listener service
                val smsServiceIntent = Intent(this, SmsListenerService::class.java)
                stopService(smsServiceIntent)
                // Stop self to prevent further monitoring until re-verification
                stopSelf()
            }

            // Update stored state
            mmkv.encode(SimStateMonitorModule.SIM_STATE_KEY, currentSimState)
            previousSimState = currentSimState
            
            // Also save to shared preferences for BootCompletedReceiver
            val prefs = getSharedPreferences("SmsListenerPrefs", Context.MODE_PRIVATE)
            prefs.edit().putString("previous_sim_state", currentSimState).apply()

            // Emit event to Flutter via EventChannel
            eventsSink?.success(mapOf(
                "event" to "onSimStateChanged",
                "data" to mapOf(
                    "currentSimState" to currentSimState,
                    "previousSimState" to previousSimState,
                    "isValid" to isValid
                )
            )) ?: run {
                Log.w(TAG, "EventSink not initialized, cannot send SIM state change event")
            }
        }
    }

    private fun startMonitoring() {
        isMonitoring = true
        monitoringThread = Thread {
            while (isMonitoring) {
                try {
                    checkSimStateAndNotify()
                    Thread.sleep(5000) // Check every 5 seconds
                } catch (e: Exception) {
                    if (isMonitoring) {
                        e.printStackTrace()
                    }
                }
            }
        }.apply { start() }
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        android.util.Log.d("SimStateMonitorService", "Service onDestroy called - Cleaning up resources")
        isMonitoring = false
        monitoringThread?.interrupt()
        monitoringThread = null
        stopForeground(true)
        super.onDestroy()
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        android.util.Log.d("SimStateMonitorService", "Service task removed - Stopping service")
        stopSelf()
        super.onTaskRemoved(rootIntent)
    }
}