# OkHttp3 rules
-keep class org.bouncycastle.jsse.** { *; }
-keep class org.conscrypt.** { *; }
-keep class org.openjsse.** { *; }

# Room rules
-keep class androidx.room.** { *; }
-keep class androidx.room.compiler.** { *; }

# MMKV rules
-keep class com.tencent.mmkv.** { *; }

# Keep all native method names and signatures
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep all classes that are referenced in the AndroidManifest.xml
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class com.android.vending.licensing.ILicensingService

# Keep all Flutter-related classes
-keep class io.flutter.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.embedding.** { *; }
-keep class io.flutter.app.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.facade.** { *; }

# Keep all Kotlin-related classes
-keep class kotlin.** { *; }
-keep class kotlin.Metadata { *; }
-keep class kotlin.jvm.functions.** { *; }
-keep class kotlin.coroutines.** { *; }
-keep class kotlinx.coroutines.** { *; }

# Keep all AndroidX classes
-keep class androidx.** { *; }

# Keep all support library classes
-keep class android.support.** { *; }

# Gson specific classes
-keep class com.google.gson.** { *; }

# OkHttp specific classes
-keep class okhttp3.** { *; }
-keep class okio.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**

# Keep the SIM card and SMS related classes
-keep class com.sbpay.** { *; }
