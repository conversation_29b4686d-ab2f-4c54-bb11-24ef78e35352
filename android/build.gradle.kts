allprojects {
    repositories {
        google()
        mavenCentral()
    }
    
    tasks.withType<com.android.build.gradle.internal.lint.AndroidLintTask> {
        enabled = false
    }
    
    subprojects {
        afterEvaluate {
            if (project.plugins.hasPlugin("com.android.application") || 
                project.plugins.hasPlugin("com.android.library")) {
                project.tasks.matching { it.name.startsWith("lint") }.all {
                    enabled = false
                }
            }
        }
    }
}

val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}
