import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'sim_card.g.dart';

@HiveType(typeId: 3)
@JsonSerializable()
class SimCardInfo extends Equatable {
  @HiveField(0)
  final int slotIndex;

  @HiveField(1)
  final String carrierName;

  @HiveField(2)
  final String displayName;

  @HiveField(3)
  final String iccId;

  @HiveField(4)
  final String countryIso;

  @HiveField(5)
  final bool isEmbedded;

  @HiveField(6)
  final int subscriptionId;

  @HiveField(7)
  final String phoneNumber;

  const SimCardInfo({
    required this.slotIndex,
    required this.carrierName,
    required this.displayName,
    required this.iccId,
    required this.countryIso,
    required this.isEmbedded,
    required this.subscriptionId,
    required this.phoneNumber,
  });

  factory SimCardInfo.fromJson(Map<String, dynamic> json) {
    return SimCardInfo(
      slotIndex: json['slotIndex'] as int? ?? 0,
      carrierName: json['carrierName'] as String? ?? '',
      displayName: json['displayName'] as String? ?? '',
      iccId: json['iccId'] as String? ?? '',
      countryIso: json['countryIso'] as String? ?? '',
      isEmbedded: json['isEmbedded'] as bool? ?? false,
      subscriptionId: json['subscriptionId'] as int? ?? 0,
      phoneNumber: json['phoneNumber'] as String? ?? '',
    );
  }
  Map<String, dynamic> toJson() => _$SimCardInfoToJson(this);

  @override
  List<Object?> get props => [
        slotIndex,
        carrierName,
        displayName,
        iccId,
        countryIso,
        isEmbedded,
        subscriptionId,
        phoneNumber,
      ];
}

@HiveType(typeId: 4)
@JsonSerializable()
class SimInfo extends Equatable {
  @HiveField(0)
  final List<SimCardInfo> simCards;

  @HiveField(1)
  final int activeSimCount;

  @HiveField(2)
  final int phoneCount;

  const SimInfo({
    required this.simCards,
    required this.activeSimCount,
    required this.phoneCount,
  });

  factory SimInfo.fromJson(Map<String, dynamic> json) {
    // Handle potential Map<Object, Object> conversion
    List<SimCardInfo> simCards = [];
    if (json['simCards'] is List) {
      simCards = (json['simCards'] as List)
          .map((e) {
            if (e == null) return null;
            // Convert Map<Object, Object> to Map<String, dynamic> if needed
            if (e is Map) {
              final converted = <String, dynamic>{};
              e.forEach((key, value) {
                converted[key.toString()] = value;
              });
              return SimCardInfo.fromJson(converted);
            }
            return null;
          })
          .where((e) => e != null)
          .toList()
          .cast<SimCardInfo>();
    }
    
    return SimInfo(
      simCards: simCards,
      activeSimCount: json['activeSimCount'] is int ? json['activeSimCount'] as int : 0,
      phoneCount: json['phoneCount'] is int ? json['phoneCount'] as int : 0,
    );
  }
  Map<String, dynamic> toJson() => _$SimInfoToJson(this);

  @override
  List<Object?> get props => [simCards, activeSimCount, phoneCount];
}

@HiveType(typeId: 5)
@JsonSerializable()
class SimStateChange extends Equatable {
  @HiveField(0)
  final String simState;

  @HiveField(1)
  final String? previousState;

  const SimStateChange({
    required this.simState,
    this.previousState,
  });

  factory SimStateChange.fromJson(Map<String, dynamic> json) {
    return SimStateChange(
      simState: json['simState'] as String? ?? '',
      previousState: json['previousState'] as String?,
    );
  }
  Map<String, dynamic> toJson() => _$SimStateChangeToJson(this);

  @override
  List<Object?> get props => [simState, previousState];
}

enum SimSlot {
  @JsonValue(0)
  slot0,
  @JsonValue(1)
  slot1,
}

extension SimSlotExtension on SimSlot {
  int get value {
    switch (this) {
      case SimSlot.slot0:
        return 0;
      case SimSlot.slot1:
        return 1;
    }
  }

  String get displayName {
    switch (this) {
      case SimSlot.slot0:
        return 'SIM 1';
      case SimSlot.slot1:
        return 'SIM 2';
    }
  }
}
