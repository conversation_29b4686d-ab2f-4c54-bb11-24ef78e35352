// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sms_message.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SmsMessageAdapter extends TypeAdapter<SmsMessage> {
  @override
  final int typeId = 6;

  @override
  SmsMessage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SmsMessage(
      id: fields[0] as String,
      sender: fields[1] as String,
      body: fields[2] as String,
      timestamp: fields[3] as DateTime,
      simSlot: fields[4] as int,
      subscriptionId: fields[5] as int,
      isSent: fields[6] as bool,
      retryCount: fields[7] as int,
      lastRetryAt: fields[8] as DateTime?,
      error: fields[9] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, SmsMessage obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.sender)
      ..writeByte(2)
      ..write(obj.body)
      ..writeByte(3)
      ..write(obj.timestamp)
      ..writeByte(4)
      ..write(obj.simSlot)
      ..writeByte(5)
      ..write(obj.subscriptionId)
      ..writeByte(6)
      ..write(obj.isSent)
      ..writeByte(7)
      ..write(obj.retryCount)
      ..writeByte(8)
      ..write(obj.lastRetryAt)
      ..writeByte(9)
      ..write(obj.error);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmsMessageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PendingSmsMessageAdapter extends TypeAdapter<PendingSmsMessage> {
  @override
  final int typeId = 7;

  @override
  PendingSmsMessage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PendingSmsMessage(
      messageId: fields[0] as String,
      sender: fields[1] as String,
      body: fields[2] as String,
      receivedAt: fields[3] as DateTime,
      simSlot: fields[4] as int,
      subscriptionId: fields[5] as int,
      retryCount: fields[6] as int,
      lastRetryAt: fields[7] as DateTime?,
      lastError: fields[8] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, PendingSmsMessage obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.messageId)
      ..writeByte(1)
      ..write(obj.sender)
      ..writeByte(2)
      ..write(obj.body)
      ..writeByte(3)
      ..write(obj.receivedAt)
      ..writeByte(4)
      ..write(obj.simSlot)
      ..writeByte(5)
      ..write(obj.subscriptionId)
      ..writeByte(6)
      ..write(obj.retryCount)
      ..writeByte(7)
      ..write(obj.lastRetryAt)
      ..writeByte(8)
      ..write(obj.lastError);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PendingSmsMessageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SmsMessage _$SmsMessageFromJson(Map<String, dynamic> json) => SmsMessage(
      id: json['id'] as String,
      sender: json['sender'] as String,
      body: json['body'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      simSlot: (json['simSlot'] as num).toInt(),
      subscriptionId: (json['subscriptionId'] as num).toInt(),
      isSent: json['isSent'] as bool? ?? false,
      retryCount: (json['retryCount'] as num?)?.toInt() ?? 0,
      lastRetryAt: json['lastRetryAt'] == null
          ? null
          : DateTime.parse(json['lastRetryAt'] as String),
      error: json['error'] as String?,
    );

Map<String, dynamic> _$SmsMessageToJson(SmsMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sender': instance.sender,
      'body': instance.body,
      'timestamp': instance.timestamp.toIso8601String(),
      'simSlot': instance.simSlot,
      'subscriptionId': instance.subscriptionId,
      'isSent': instance.isSent,
      'retryCount': instance.retryCount,
      'lastRetryAt': instance.lastRetryAt?.toIso8601String(),
      'error': instance.error,
    };

PendingSmsMessage _$PendingSmsMessageFromJson(Map<String, dynamic> json) =>
    PendingSmsMessage(
      messageId: json['messageId'] as String,
      sender: json['sender'] as String,
      body: json['body'] as String,
      receivedAt: DateTime.parse(json['receivedAt'] as String),
      simSlot: (json['simSlot'] as num).toInt(),
      subscriptionId: (json['subscriptionId'] as num).toInt(),
      retryCount: (json['retryCount'] as num?)?.toInt() ?? 0,
      lastRetryAt: json['lastRetryAt'] == null
          ? null
          : DateTime.parse(json['lastRetryAt'] as String),
      lastError: json['lastError'] as String?,
    );

Map<String, dynamic> _$PendingSmsMessageToJson(PendingSmsMessage instance) =>
    <String, dynamic>{
      'messageId': instance.messageId,
      'sender': instance.sender,
      'body': instance.body,
      'receivedAt': instance.receivedAt.toIso8601String(),
      'simSlot': instance.simSlot,
      'subscriptionId': instance.subscriptionId,
      'retryCount': instance.retryCount,
      'lastRetryAt': instance.lastRetryAt?.toIso8601String(),
      'lastError': instance.lastError,
    };
