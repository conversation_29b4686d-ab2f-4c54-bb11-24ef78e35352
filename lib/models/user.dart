import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'user.g.dart';

@HiveType(typeId: 8)
@JsonSerializable()
class Hierarchy extends Equatable {
  @HiveField(0)
  final String? supervisor;

  @HiveField(1)
  final SuperAdmin? superAdmin;

  @HiveField(2)
  final Client? client;

  const Hierarchy({
    this.supervisor,
    this.superAdmin,
    this.client,
  });

  factory Hierarchy.fromJson(Map<String, dynamic> json) {
    return Hierarchy(
      supervisor: json['supervisor'] as String?,
      superAdmin: json['SUPER_ADMIN'] != null
          ? SuperAdmin.fromJson(json['SUPER_ADMIN'] as Map<String, dynamic>)
          : null,
      client: json['CLIENT'] != null
          ? Client.fromJson(json['CLIENT'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'supervisor': supervisor,
      'SUPER_ADMIN': superAdmin?.toJson(),
      'CLIENT': client?.toJson(),
    };
  }

  @override
  List<Object?> get props => [supervisor, superAdmin, client];
}

@HiveType(typeId: 9)
@JsonSerializable()
class SuperAdmin extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String email;

  const SuperAdmin({
    required this.id,
    required this.email,
  });

  factory SuperAdmin.fromJson(Map<String, dynamic> json) {
    return SuperAdmin(
      id: json['_id'] as String? ?? '',
      email: json['email'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'email': email,
    };
  }

  @override
  List<Object?> get props => [id, email];
}

@HiveType(typeId: 10)
@JsonSerializable()
class Client extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String email;

  const Client({
    required this.id,
    required this.email,
  });

  factory Client.fromJson(Map<String, dynamic> json) {
    return Client(
      id: json['_id'] as String? ?? '',
      email: json['email'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'email': email,
    };
  }

  @override
  List<Object?> get props => [id, email];
}

@HiveType(typeId: 11)
@JsonSerializable()
class PhoneDetail extends Equatable {
  @HiveField(0)
  final String phone;

  @HiveField(1)
  final bool status;

  const PhoneDetail({
    required this.phone,
    required this.status,
  });

  factory PhoneDetail.fromJson(Map<String, dynamic> json) {
    return PhoneDetail(
      phone: json['phone'] as String? ?? '',
      status: json['status'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'phone': phone,
      'status': status,
    };
  }

  @override
  List<Object?> get props => [phone, status];
}

@HiveType(typeId: 12)
@JsonSerializable()
class PrimaryArrayNumber extends Equatable {
  @HiveField(0)
  final String phone;

  @HiveField(1)
  final bool isPending;

  @HiveField(2)
  final bool isSuccess;

  const PrimaryArrayNumber({
    required this.phone,
    required this.isPending,
    required this.isSuccess,
  });

  factory PrimaryArrayNumber.fromJson(Map<String, dynamic> json) {
    return PrimaryArrayNumber(
      phone: json['phone'] as String? ?? '',
      isPending: json['is_pending'] as bool? ?? false,
      isSuccess: json['is_success'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'phone': phone,
      'is_pending': isPending,
      'is_success': isSuccess,
    };
  }

  @override
  List<Object?> get props => [phone, isPending, isSuccess];
}

@HiveType(typeId: 0)
@JsonSerializable()
class User extends Equatable {
  @HiveField(0)
  @JsonKey(name: '_id')
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String email;

  @HiveField(3)
  final String role;

  @HiveField(4)
  final String? domain;

  @HiveField(5)
  @JsonKey(name: 'phone_number')
  final String? phoneNumber;

  @HiveField(6)
  final PaymentConfig? payment;

  @HiveField(7)
  final String token;

  @HiveField(8)
  @JsonKey(name: 'verifiedSims')
  final List<VerifiedSim> verifiedSims;

  // Additional fields from API response
  @HiveField(9)
  final String? apiKey;

  @HiveField(10)
  final String? redirectUrl;

  @HiveField(11)
  final String? phoneCountryCode;

  @HiveField(12)
  final String? phoneLocalNumber;

  @HiveField(13)
  final List<String>? phoneNumberArray;

  @HiveField(14)
  final bool? isSandbox;

  @HiveField(15)
  final int? wallet;

  @HiveField(16)
  final bool? isPasswordChanged;

  @HiveField(17)
  final Hierarchy? hierarchy;

  @HiveField(18)
  final bool? allowNegativeBalance;

  @HiveField(19)
  final int? holdBalance;

  @HiveField(20)
  final bool? isUnattendedProcessing;

  @HiveField(21)
  final String? primaryNumber;

  @HiveField(22)
  final bool? isBlocked;

  @HiveField(23)
  final bool? isDeleted;

  @HiveField(24)
  final List<PhoneDetail>? phoneDetails;

  @HiveField(25)
  final String? fcmToken;

  @HiveField(26)
  final bool? isResetLimit;

  @HiveField(27)
  final List<PrimaryArrayNumber>? primaryArrayNumber;

  const User({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    this.domain,
    this.phoneNumber,
    this.payment,
    required this.token,
    required this.verifiedSims,
    this.apiKey,
    this.redirectUrl,
    this.phoneCountryCode,
    this.phoneLocalNumber,
    this.phoneNumberArray,
    this.isSandbox,
    this.wallet,
    this.isPasswordChanged,
    this.hierarchy,
    this.allowNegativeBalance,
    this.holdBalance,
    this.isUnattendedProcessing,
    this.primaryNumber,
    this.isBlocked,
    this.isDeleted,
    this.phoneDetails,
    this.fcmToken,
    this.isResetLimit,
    this.primaryArrayNumber,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['_id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      email: json['email'] as String? ?? '',
      role: json['role'] as String? ?? '',
      domain: json['domain'] as String?,
      phoneNumber: json['phone_number'] as String?,
      payment: json['payment'] != null
          ? PaymentConfig.fromJson(json['payment'] as Map<String, dynamic>)
          : null,
      token: json['token'] as String? ?? '',
      verifiedSims: (json['verifiedSims'] as List<dynamic>?)
              ?.map((e) => e != null ? VerifiedSim.fromJson(e as Map<String, dynamic>) : null)
              .where((e) => e != null)
              .toList()
              .cast<VerifiedSim>() ??
          [],
      apiKey: json['apiKey'] as String?,
      redirectUrl: json['redirect_url'] as String?,
      phoneCountryCode: json['phone_country_code'] as String?,
      phoneLocalNumber: json['phone_local_number'] as String?,
      phoneNumberArray: (json['phone_number_array'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .where((e) => e != null)
          .toList()
          .cast<String>() ?? [],
      isSandbox: json['is_sandbox'] as bool?,
      wallet: json['wallet'] as int?,
      isPasswordChanged: json['isPasswordChanged'] as bool?,
      hierarchy: json['hierarchy'] != null
          ? Hierarchy.fromJson(json['hierarchy'] as Map<String, dynamic>)
          : null,
      allowNegativeBalance: json['allow_negative_balance'] as bool?,
      holdBalance: json['hold_balance'] as int?,
      isUnattendedProcessing: json['is_unattended_proccessing'] as bool?,
      primaryNumber: json['primary_number'] as String?,
      isBlocked: json['is_blocked'] as bool?,
      isDeleted: json['is_deleted'] as bool?,
      phoneDetails: (json['phone_details'] as List<dynamic>?)
          ?.map((e) => e != null ? PhoneDetail.fromJson(e as Map<String, dynamic>) : null)
          .where((e) => e != null)
          .toList()
          .cast<PhoneDetail>() ?? [],
      fcmToken: json['fcm_token'] as String?,
      isResetLimit: json['is_reset_limit'] as bool?,
      primaryArrayNumber: (json['primary_array_number'] as List<dynamic>?)
          ?.map((e) => e != null ? PrimaryArrayNumber.fromJson(e as Map<String, dynamic>) : null)
          .where((e) => e != null)
          .toList()
          .cast<PrimaryArrayNumber>() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'email': email,
      'role': role,
      'domain': domain,
      'phone_number': phoneNumber,
      'payment': payment?.toJson(),
      'token': token,
      'verifiedSims': verifiedSims.map((e) => e.toJson()).toList(),
      'apiKey': apiKey,
      'redirect_url': redirectUrl,
      'phone_country_code': phoneCountryCode,
      'phone_local_number': phoneLocalNumber,
      'phone_number_array': phoneNumberArray,
      'is_sandbox': isSandbox,
      'wallet': wallet,
      'isPasswordChanged': isPasswordChanged,
      'hierarchy': hierarchy?.toJson(),
      'allow_negative_balance': allowNegativeBalance,
      'hold_balance': holdBalance,
      'is_unattended_proccessing': isUnattendedProcessing,
      'primary_number': primaryNumber,
      'is_blocked': isBlocked,
      'is_deleted': isDeleted,
      'phone_details': phoneDetails?.map((e) => e.toJson()).toList(),
      'fcm_token': fcmToken,
      'is_reset_limit': isResetLimit,
      'primary_array_number': primaryArrayNumber?.map((e) => e.toJson()).toList(),
    };
  }

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? role,
    String? domain,
    String? phoneNumber,
    PaymentConfig? payment,
    String? token,
    List<VerifiedSim>? verifiedSims,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      role: role ?? this.role,
      domain: domain ?? this.domain,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      payment: payment ?? this.payment,
      token: token ?? this.token,
      verifiedSims: verifiedSims ?? this.verifiedSims,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        role,
        domain,
        phoneNumber,
        payment,
        token,
        verifiedSims,
      ];
}

@HiveType(typeId: 1)
@JsonSerializable()
class PaymentConfig extends Equatable {
  @HiveField(0)
  final String baseUrl;

  @HiveField(1)
  final String apiKey;

  const PaymentConfig({
    required this.baseUrl,
    required this.apiKey,
  });

  factory PaymentConfig.fromJson(Map<String, dynamic> json) {
    return PaymentConfig(
      baseUrl: json['baseUrl'] as String? ?? '',
      apiKey: json['apiKey'] as String? ?? '',
    );
  }
  Map<String, dynamic> toJson() => _$PaymentConfigToJson(this);

  @override
  List<Object?> get props => [baseUrl, apiKey];
}

@HiveType(typeId: 2)
@JsonSerializable()
class VerifiedSim extends Equatable {
  @HiveField(0)
  @JsonKey(name: 'phone_number')
  final String phoneNumber;

  @HiveField(1)
  final int subscriptionId;

  @HiveField(2)
  final String iccId;

  @HiveField(3)
  final int slotIndex;

  @HiveField(4)
  final String? carrierName;

  @HiveField(5)
  final String? simSerialNumber;

  @HiveField(6)
  final String verifiedAt;

  @HiveField(7)
  final String simSignature;

  @HiveField(8)
  final bool isVerified;

  const VerifiedSim({
    required this.phoneNumber,
    required this.subscriptionId,
    required this.iccId,
    required this.slotIndex,
    this.carrierName,
    this.simSerialNumber,
    required this.verifiedAt,
    required this.simSignature,
    required this.isVerified,
  });

  factory VerifiedSim.fromJson(Map<String, dynamic> json) {
    return VerifiedSim(
      phoneNumber: json['phone_number'] as String? ?? '',
      subscriptionId: json['subscriptionId'] as int? ?? 0,
      iccId: json['iccId'] as String? ?? '',
      slotIndex: json['slotIndex'] as int? ?? 0,
      carrierName: json['carrierName'] as String?,
      simSerialNumber: json['simSerialNumber'] as String?,
      verifiedAt: json['verifiedAt'] as String? ?? '',
      simSignature: json['simSignature'] as String? ?? '',
      isVerified: json['isVerified'] as bool? ?? false,
    );
  }
  Map<String, dynamic> toJson() => _$VerifiedSimToJson(this);

  VerifiedSim copyWith({
    String? phoneNumber,
    int? subscriptionId,
    String? iccId,
    int? slotIndex,
    String? carrierName,
    String? simSerialNumber,
    String? verifiedAt,
    String? simSignature,
    bool? isVerified,
  }) {
    return VerifiedSim(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      subscriptionId: subscriptionId ?? this.subscriptionId,
      iccId: iccId ?? this.iccId,
      slotIndex: slotIndex ?? this.slotIndex,
      carrierName: carrierName ?? this.carrierName,
      simSerialNumber: simSerialNumber ?? this.simSerialNumber,
      verifiedAt: verifiedAt ?? this.verifiedAt,
      simSignature: simSignature ?? this.simSignature,
      isVerified: isVerified ?? this.isVerified,
    );
  }

  @override
  List<Object?> get props => [
        phoneNumber,
        subscriptionId,
        iccId,
        slotIndex,
        carrierName,
        simSerialNumber,
        verifiedAt,
        simSignature,
        isVerified,
      ];
}
