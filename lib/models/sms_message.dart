import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'sms_message.g.dart';

@HiveType(typeId: 6)
@JsonSerializable()
class SmsMessage extends Equatable {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String sender;

  @HiveField(2)
  final String body;

  @HiveField(3)
  final DateTime timestamp;

  @HiveField(4)
  final int simSlot;

  @HiveField(5)
  final int subscriptionId;

  @HiveField(6)
  final bool isSent;

  @HiveField(7)
  final int retryCount;

  @HiveField(8)
  final DateTime? lastRetryAt;

  @HiveField(9)
  final String? error;

  const SmsMessage({
    required this.id,
    required this.sender,
    required this.body,
    required this.timestamp,
    required this.simSlot,
    required this.subscriptionId,
    this.isSent = false,
    this.retryCount = 0,
    this.lastRetryAt,
    this.error,
  });

  factory SmsMessage.fromJson(Map<String, dynamic> json) {
    return SmsMessage(
      id: json['id'] as String? ?? '',
      sender: json['sender'] as String? ?? '',
      body: json['body'] as String? ?? '',
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'] as String)
          : DateTime.now(),
      simSlot: json['simSlot'] as int? ?? 0,
      subscriptionId: json['subscriptionId'] as int? ?? 0,
      isSent: json['isSent'] as bool? ?? false,
      retryCount: json['retryCount'] as int? ?? 0,
      lastRetryAt: json['lastRetryAt'] != null
          ? DateTime.parse(json['lastRetryAt'] as String)
          : null,
      error: json['error'] as String?,
    );
  }
  Map<String, dynamic> toJson() => _$SmsMessageToJson(this);

  SmsMessage copyWith({
    String? id,
    String? sender,
    String? body,
    DateTime? timestamp,
    int? simSlot,
    int? subscriptionId,
    bool? isSent,
    int? retryCount,
    DateTime? lastRetryAt,
    String? error,
  }) {
    return SmsMessage(
      id: id ?? this.id,
      sender: sender ?? this.sender,
      body: body ?? this.body,
      timestamp: timestamp ?? this.timestamp,
      simSlot: simSlot ?? this.simSlot,
      subscriptionId: subscriptionId ?? this.subscriptionId,
      isSent: isSent ?? this.isSent,
      retryCount: retryCount ?? this.retryCount,
      lastRetryAt: lastRetryAt ?? this.lastRetryAt,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [
        id,
        sender,
        body,
        timestamp,
        simSlot,
        subscriptionId,
        isSent,
        retryCount,
        lastRetryAt,
        error,
      ];
}

@HiveType(typeId: 7)
@JsonSerializable()
class PendingSmsMessage extends Equatable {
  @HiveField(0)
  final String messageId;

  @HiveField(1)
  final String sender;

  @HiveField(2)
  final String body;

  @HiveField(3)
  final DateTime receivedAt;

  @HiveField(4)
  final int simSlot;

  @HiveField(5)
  final int subscriptionId;

  @HiveField(6)
  final int retryCount;

  @HiveField(7)
  final DateTime? lastRetryAt;

  @HiveField(8)
  final String? lastError;

  const PendingSmsMessage({
    required this.messageId,
    required this.sender,
    required this.body,
    required this.receivedAt,
    required this.simSlot,
    required this.subscriptionId,
    this.retryCount = 0,
    this.lastRetryAt,
    this.lastError,
  });

  factory PendingSmsMessage.fromJson(Map<String, dynamic> json) {
    return PendingSmsMessage(
      messageId: json['messageId'] as String? ?? '',
      sender: json['sender'] as String? ?? '',
      body: json['body'] as String? ?? '',
      receivedAt: json['receivedAt'] != null
          ? DateTime.parse(json['receivedAt'] as String)
          : DateTime.now(),
      simSlot: json['simSlot'] as int? ?? 0,
      subscriptionId: json['subscriptionId'] as int? ?? 0,
      retryCount: json['retryCount'] as int? ?? 0,
      lastRetryAt: json['lastRetryAt'] != null
          ? DateTime.parse(json['lastRetryAt'] as String)
          : null,
      lastError: json['lastError'] as String?,
    );
  }
  Map<String, dynamic> toJson() => _$PendingSmsMessageToJson(this);

  PendingSmsMessage copyWith({
    String? messageId,
    String? sender,
    String? body,
    DateTime? receivedAt,
    int? simSlot,
    int? subscriptionId,
    int? retryCount,
    DateTime? lastRetryAt,
    String? lastError,
  }) {
    return PendingSmsMessage(
      messageId: messageId ?? this.messageId,
      sender: sender ?? this.sender,
      body: body ?? this.body,
      receivedAt: receivedAt ?? this.receivedAt,
      simSlot: simSlot ?? this.simSlot,
      subscriptionId: subscriptionId ?? this.subscriptionId,
      retryCount: retryCount ?? this.retryCount,
      lastRetryAt: lastRetryAt ?? this.lastRetryAt,
      lastError: lastError ?? this.lastError,
    );
  }

  @override
  List<Object?> get props => [
        messageId,
        sender,
        body,
        receivedAt,
        simSlot,
        subscriptionId,
        retryCount,
        lastRetryAt,
        lastError,
      ];
}
