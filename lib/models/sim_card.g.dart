// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sim_card.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SimCardInfoAdapter extends TypeAdapter<SimCardInfo> {
  @override
  final int typeId = 3;

  @override
  SimCardInfo read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SimCardInfo(
      slotIndex: fields[0] as int,
      carrierName: fields[1] as String,
      displayName: fields[2] as String,
      iccId: fields[3] as String,
      countryIso: fields[4] as String,
      isEmbedded: fields[5] as bool,
      subscriptionId: fields[6] as int,
      phoneNumber: fields[7] as String,
    );
  }

  @override
  void write(BinaryWriter writer, SimCardInfo obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.slotIndex)
      ..writeByte(1)
      ..write(obj.carrierName)
      ..writeByte(2)
      ..write(obj.displayName)
      ..writeByte(3)
      ..write(obj.iccId)
      ..writeByte(4)
      ..write(obj.countryIso)
      ..writeByte(5)
      ..write(obj.isEmbedded)
      ..writeByte(6)
      ..write(obj.subscriptionId)
      ..writeByte(7)
      ..write(obj.phoneNumber);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SimCardInfoAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SimInfoAdapter extends TypeAdapter<SimInfo> {
  @override
  final int typeId = 4;

  @override
  SimInfo read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SimInfo(
      simCards: (fields[0] as List).cast<SimCardInfo>(),
      activeSimCount: fields[1] as int,
      phoneCount: fields[2] as int,
    );
  }

  @override
  void write(BinaryWriter writer, SimInfo obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.simCards)
      ..writeByte(1)
      ..write(obj.activeSimCount)
      ..writeByte(2)
      ..write(obj.phoneCount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SimInfoAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SimStateChangeAdapter extends TypeAdapter<SimStateChange> {
  @override
  final int typeId = 5;

  @override
  SimStateChange read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SimStateChange(
      simState: fields[0] as String,
      previousState: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, SimStateChange obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.simState)
      ..writeByte(1)
      ..write(obj.previousState);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SimStateChangeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SimCardInfo _$SimCardInfoFromJson(Map<String, dynamic> json) => SimCardInfo(
      slotIndex: (json['slotIndex'] as num).toInt(),
      carrierName: json['carrierName'] as String,
      displayName: json['displayName'] as String,
      iccId: json['iccId'] as String,
      countryIso: json['countryIso'] as String,
      isEmbedded: json['isEmbedded'] as bool,
      subscriptionId: (json['subscriptionId'] as num).toInt(),
      phoneNumber: json['phoneNumber'] as String,
    );

Map<String, dynamic> _$SimCardInfoToJson(SimCardInfo instance) =>
    <String, dynamic>{
      'slotIndex': instance.slotIndex,
      'carrierName': instance.carrierName,
      'displayName': instance.displayName,
      'iccId': instance.iccId,
      'countryIso': instance.countryIso,
      'isEmbedded': instance.isEmbedded,
      'subscriptionId': instance.subscriptionId,
      'phoneNumber': instance.phoneNumber,
    };

SimInfo _$SimInfoFromJson(Map<String, dynamic> json) => SimInfo(
      simCards: (json['simCards'] as List<dynamic>)
          .map((e) => SimCardInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      activeSimCount: (json['activeSimCount'] as num).toInt(),
      phoneCount: (json['phoneCount'] as num).toInt(),
    );

Map<String, dynamic> _$SimInfoToJson(SimInfo instance) => <String, dynamic>{
      'simCards': instance.simCards,
      'activeSimCount': instance.activeSimCount,
      'phoneCount': instance.phoneCount,
    };

SimStateChange _$SimStateChangeFromJson(Map<String, dynamic> json) =>
    SimStateChange(
      simState: json['simState'] as String,
      previousState: json['previousState'] as String?,
    );

Map<String, dynamic> _$SimStateChangeToJson(SimStateChange instance) =>
    <String, dynamic>{
      'simState': instance.simState,
      'previousState': instance.previousState,
    };
