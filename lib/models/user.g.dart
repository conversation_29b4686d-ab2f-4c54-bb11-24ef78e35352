// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HierarchyAdapter extends TypeAdapter<Hierarchy> {
  @override
  final int typeId = 8;

  @override
  Hierarchy read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Hierarchy(
      supervisor: fields[0] as String?,
      superAdmin: fields[1] as SuperAdmin?,
      client: fields[2] as Client?,
    );
  }

  @override
  void write(BinaryWriter writer, Hierarchy obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.supervisor)
      ..writeByte(1)
      ..write(obj.superAdmin)
      ..writeByte(2)
      ..write(obj.client);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HierarchyAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SuperAdminAdapter extends TypeAdapter<SuperAdmin> {
  @override
  final int typeId = 9;

  @override
  SuperAdmin read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SuperAdmin(
      id: fields[0] as String,
      email: fields[1] as String,
    );
  }

  @override
  void write(BinaryWriter writer, SuperAdmin obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.email);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SuperAdminAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ClientAdapter extends TypeAdapter<Client> {
  @override
  final int typeId = 10;

  @override
  Client read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Client(
      id: fields[0] as String,
      email: fields[1] as String,
    );
  }

  @override
  void write(BinaryWriter writer, Client obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.email);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ClientAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PhoneDetailAdapter extends TypeAdapter<PhoneDetail> {
  @override
  final int typeId = 11;

  @override
  PhoneDetail read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PhoneDetail(
      phone: fields[0] as String,
      status: fields[1] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, PhoneDetail obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.phone)
      ..writeByte(1)
      ..write(obj.status);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PhoneDetailAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PrimaryArrayNumberAdapter extends TypeAdapter<PrimaryArrayNumber> {
  @override
  final int typeId = 12;

  @override
  PrimaryArrayNumber read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PrimaryArrayNumber(
      phone: fields[0] as String,
      isPending: fields[1] as bool,
      isSuccess: fields[2] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, PrimaryArrayNumber obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.phone)
      ..writeByte(1)
      ..write(obj.isPending)
      ..writeByte(2)
      ..write(obj.isSuccess);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PrimaryArrayNumberAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserAdapter extends TypeAdapter<User> {
  @override
  final int typeId = 0;

  @override
  User read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return User(
      id: fields[0] as String,
      name: fields[1] as String,
      email: fields[2] as String,
      role: fields[3] as String,
      domain: fields[4] as String?,
      phoneNumber: fields[5] as String?,
      payment: fields[6] as PaymentConfig?,
      token: fields[7] as String,
      verifiedSims: (fields[8] as List).cast<VerifiedSim>(),
      apiKey: fields[9] as String?,
      redirectUrl: fields[10] as String?,
      phoneCountryCode: fields[11] as String?,
      phoneLocalNumber: fields[12] as String?,
      phoneNumberArray: (fields[13] as List?)?.cast<String>(),
      isSandbox: fields[14] as bool?,
      wallet: fields[15] as int?,
      isPasswordChanged: fields[16] as bool?,
      hierarchy: fields[17] as Hierarchy?,
      allowNegativeBalance: fields[18] as bool?,
      holdBalance: fields[19] as int?,
      isUnattendedProcessing: fields[20] as bool?,
      primaryNumber: fields[21] as String?,
      isBlocked: fields[22] as bool?,
      isDeleted: fields[23] as bool?,
      phoneDetails: (fields[24] as List?)?.cast<PhoneDetail>(),
      fcmToken: fields[25] as String?,
      isResetLimit: fields[26] as bool?,
      primaryArrayNumber: (fields[27] as List?)?.cast<PrimaryArrayNumber>(),
    );
  }

  @override
  void write(BinaryWriter writer, User obj) {
    writer
      ..writeByte(28)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.email)
      ..writeByte(3)
      ..write(obj.role)
      ..writeByte(4)
      ..write(obj.domain)
      ..writeByte(5)
      ..write(obj.phoneNumber)
      ..writeByte(6)
      ..write(obj.payment)
      ..writeByte(7)
      ..write(obj.token)
      ..writeByte(8)
      ..write(obj.verifiedSims)
      ..writeByte(9)
      ..write(obj.apiKey)
      ..writeByte(10)
      ..write(obj.redirectUrl)
      ..writeByte(11)
      ..write(obj.phoneCountryCode)
      ..writeByte(12)
      ..write(obj.phoneLocalNumber)
      ..writeByte(13)
      ..write(obj.phoneNumberArray)
      ..writeByte(14)
      ..write(obj.isSandbox)
      ..writeByte(15)
      ..write(obj.wallet)
      ..writeByte(16)
      ..write(obj.isPasswordChanged)
      ..writeByte(17)
      ..write(obj.hierarchy)
      ..writeByte(18)
      ..write(obj.allowNegativeBalance)
      ..writeByte(19)
      ..write(obj.holdBalance)
      ..writeByte(20)
      ..write(obj.isUnattendedProcessing)
      ..writeByte(21)
      ..write(obj.primaryNumber)
      ..writeByte(22)
      ..write(obj.isBlocked)
      ..writeByte(23)
      ..write(obj.isDeleted)
      ..writeByte(24)
      ..write(obj.phoneDetails)
      ..writeByte(25)
      ..write(obj.fcmToken)
      ..writeByte(26)
      ..write(obj.isResetLimit)
      ..writeByte(27)
      ..write(obj.primaryArrayNumber);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentConfigAdapter extends TypeAdapter<PaymentConfig> {
  @override
  final int typeId = 1;

  @override
  PaymentConfig read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentConfig(
      baseUrl: fields[0] as String,
      apiKey: fields[1] as String,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentConfig obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.baseUrl)
      ..writeByte(1)
      ..write(obj.apiKey);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentConfigAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class VerifiedSimAdapter extends TypeAdapter<VerifiedSim> {
  @override
  final int typeId = 2;

  @override
  VerifiedSim read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return VerifiedSim(
      phoneNumber: fields[0] as String,
      subscriptionId: fields[1] as int,
      iccId: fields[2] as String,
      slotIndex: fields[3] as int,
      carrierName: fields[4] as String?,
      simSerialNumber: fields[5] as String?,
      verifiedAt: fields[6] as String,
      simSignature: fields[7] as String,
      isVerified: fields[8] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, VerifiedSim obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.phoneNumber)
      ..writeByte(1)
      ..write(obj.subscriptionId)
      ..writeByte(2)
      ..write(obj.iccId)
      ..writeByte(3)
      ..write(obj.slotIndex)
      ..writeByte(4)
      ..write(obj.carrierName)
      ..writeByte(5)
      ..write(obj.simSerialNumber)
      ..writeByte(6)
      ..write(obj.verifiedAt)
      ..writeByte(7)
      ..write(obj.simSignature)
      ..writeByte(8)
      ..write(obj.isVerified);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VerifiedSimAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Hierarchy _$HierarchyFromJson(Map<String, dynamic> json) => Hierarchy(
      supervisor: json['supervisor'] as String?,
      superAdmin: json['superAdmin'] == null
          ? null
          : SuperAdmin.fromJson(json['superAdmin'] as Map<String, dynamic>),
      client: json['client'] == null
          ? null
          : Client.fromJson(json['client'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$HierarchyToJson(Hierarchy instance) => <String, dynamic>{
      'supervisor': instance.supervisor,
      'superAdmin': instance.superAdmin,
      'client': instance.client,
    };

SuperAdmin _$SuperAdminFromJson(Map<String, dynamic> json) => SuperAdmin(
      id: json['id'] as String,
      email: json['email'] as String,
    );

Map<String, dynamic> _$SuperAdminToJson(SuperAdmin instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
    };

Client _$ClientFromJson(Map<String, dynamic> json) => Client(
      id: json['id'] as String,
      email: json['email'] as String,
    );

Map<String, dynamic> _$ClientToJson(Client instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
    };

PhoneDetail _$PhoneDetailFromJson(Map<String, dynamic> json) => PhoneDetail(
      phone: json['phone'] as String,
      status: json['status'] as bool,
    );

Map<String, dynamic> _$PhoneDetailToJson(PhoneDetail instance) =>
    <String, dynamic>{
      'phone': instance.phone,
      'status': instance.status,
    };

PrimaryArrayNumber _$PrimaryArrayNumberFromJson(Map<String, dynamic> json) =>
    PrimaryArrayNumber(
      phone: json['phone'] as String,
      isPending: json['isPending'] as bool,
      isSuccess: json['isSuccess'] as bool,
    );

Map<String, dynamic> _$PrimaryArrayNumberToJson(PrimaryArrayNumber instance) =>
    <String, dynamic>{
      'phone': instance.phone,
      'isPending': instance.isPending,
      'isSuccess': instance.isSuccess,
    };

User _$UserFromJson(Map<String, dynamic> json) => User(
      id: json['_id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      role: json['role'] as String,
      domain: json['domain'] as String?,
      phoneNumber: json['phone_number'] as String?,
      payment: json['payment'] == null
          ? null
          : PaymentConfig.fromJson(json['payment'] as Map<String, dynamic>),
      token: json['token'] as String,
      verifiedSims: (json['verifiedSims'] as List<dynamic>)
          .map((e) => VerifiedSim.fromJson(e as Map<String, dynamic>))
          .toList(),
      apiKey: json['apiKey'] as String?,
      redirectUrl: json['redirectUrl'] as String?,
      phoneCountryCode: json['phoneCountryCode'] as String?,
      phoneLocalNumber: json['phoneLocalNumber'] as String?,
      phoneNumberArray: (json['phoneNumberArray'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      isSandbox: json['isSandbox'] as bool?,
      wallet: (json['wallet'] as num?)?.toInt(),
      isPasswordChanged: json['isPasswordChanged'] as bool?,
      hierarchy: json['hierarchy'] == null
          ? null
          : Hierarchy.fromJson(json['hierarchy'] as Map<String, dynamic>),
      allowNegativeBalance: json['allowNegativeBalance'] as bool?,
      holdBalance: (json['holdBalance'] as num?)?.toInt(),
      isUnattendedProcessing: json['isUnattendedProcessing'] as bool?,
      primaryNumber: json['primaryNumber'] as String?,
      isBlocked: json['isBlocked'] as bool?,
      isDeleted: json['isDeleted'] as bool?,
      phoneDetails: (json['phoneDetails'] as List<dynamic>?)
          ?.map((e) => PhoneDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
      fcmToken: json['fcmToken'] as String?,
      isResetLimit: json['isResetLimit'] as bool?,
      primaryArrayNumber: (json['primaryArrayNumber'] as List<dynamic>?)
          ?.map((e) => PrimaryArrayNumber.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      '_id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'role': instance.role,
      'domain': instance.domain,
      'phone_number': instance.phoneNumber,
      'payment': instance.payment,
      'token': instance.token,
      'verifiedSims': instance.verifiedSims,
      'apiKey': instance.apiKey,
      'redirectUrl': instance.redirectUrl,
      'phoneCountryCode': instance.phoneCountryCode,
      'phoneLocalNumber': instance.phoneLocalNumber,
      'phoneNumberArray': instance.phoneNumberArray,
      'isSandbox': instance.isSandbox,
      'wallet': instance.wallet,
      'isPasswordChanged': instance.isPasswordChanged,
      'hierarchy': instance.hierarchy,
      'allowNegativeBalance': instance.allowNegativeBalance,
      'holdBalance': instance.holdBalance,
      'isUnattendedProcessing': instance.isUnattendedProcessing,
      'primaryNumber': instance.primaryNumber,
      'isBlocked': instance.isBlocked,
      'isDeleted': instance.isDeleted,
      'phoneDetails': instance.phoneDetails,
      'fcmToken': instance.fcmToken,
      'isResetLimit': instance.isResetLimit,
      'primaryArrayNumber': instance.primaryArrayNumber,
    };

PaymentConfig _$PaymentConfigFromJson(Map<String, dynamic> json) =>
    PaymentConfig(
      baseUrl: json['baseUrl'] as String,
      apiKey: json['apiKey'] as String,
    );

Map<String, dynamic> _$PaymentConfigToJson(PaymentConfig instance) =>
    <String, dynamic>{
      'baseUrl': instance.baseUrl,
      'apiKey': instance.apiKey,
    };

VerifiedSim _$VerifiedSimFromJson(Map<String, dynamic> json) => VerifiedSim(
      phoneNumber: json['phone_number'] as String,
      subscriptionId: (json['subscriptionId'] as num).toInt(),
      iccId: json['iccId'] as String,
      slotIndex: (json['slotIndex'] as num).toInt(),
      carrierName: json['carrierName'] as String?,
      simSerialNumber: json['simSerialNumber'] as String?,
      verifiedAt: json['verifiedAt'] as String,
      simSignature: json['simSignature'] as String,
      isVerified: json['isVerified'] as bool,
    );

Map<String, dynamic> _$VerifiedSimToJson(VerifiedSim instance) =>
    <String, dynamic>{
      'phone_number': instance.phoneNumber,
      'subscriptionId': instance.subscriptionId,
      'iccId': instance.iccId,
      'slotIndex': instance.slotIndex,
      'carrierName': instance.carrierName,
      'simSerialNumber': instance.simSerialNumber,
      'verifiedAt': instance.verifiedAt,
      'simSignature': instance.simSignature,
      'isVerified': instance.isVerified,
    };
