import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:share_plus/share_plus.dart';

class NativeLogger {
  static const MethodChannel _channel = MethodChannel('com.sbpay/native');
  static final NativeLogger _instance = NativeLogger._internal();

  factory NativeLogger() => _instance;
  NativeLogger._internal();

  /// Write a log entry to the native logger
  Future<bool> log(String tag, String message) async {
    try {
      final result = await _channel.invokeMethod('writeLog', {
        'tag': tag,
        'message': message,
      });
      return result == true;
    } catch (e) {
      debugPrint('NativeLogger: Failed to write log: $e');
      return false;
    }
  }

  /// Clear old log files (older than retention days)
  Future<bool> clearOldLogs({int retentionDays = 3}) async {
    try {
      final result = await _channel.invokeMethod('clearOldLogs', {
        'retentionDays': retentionDays,
      });
      return result == true;
    } catch (e) {
      debugPrint('NativeLogger: Failed to clear old logs: $e');
      return false;
    }
  }

  /// Get the current log file path for sharing
  Future<String?> getLogFilePath() async {
    try {
      final result = await _channel.invokeMethod('getLogFilePath');
      return result as String?;
    } catch (e) {
      debugPrint('NativeLogger: Failed to get log file path: $e');
      return null;
    }
  }

  /// Get a shareable log file path (copies to external cache)
  Future<String?> getShareableLogFilePath() async {
    try {
      final result = await _channel.invokeMethod('getShareableLogFilePath');
      return result as String?;
    } catch (e) {
      debugPrint('NativeLogger: Failed to get shareable log file path: $e');
      return null;
    }
  }

  Future<File?> getShareableLogFile() async {
  try {
    String? filePath;

    if (Platform.isAndroid) {
      // Prefer shareable path from native side (if implemented)
      filePath = await getShareableLogFilePath();
      debugPrint('NativeLogger: Got shareable path: $filePath');
    } else {
      filePath = await getLogFilePath();
    }

    if (filePath != null && await File(filePath).exists()) {
      debugPrint('NativeLogger: Returning log file: $filePath');
      return File(filePath);
    } else {
      debugPrint('NativeLogger: No log file available');
      return null;
    }
  } catch (e) {
    debugPrint('NativeLogger: Failed to get shareable log file: $e');
    return null;
  }
}


  /// Get all available log files
  Future<List<String>> getAllLogFiles() async {
    try {
      final result = await _channel.invokeMethod('getAllLogFiles');
      if (result is List) {
        return result.cast<String>();
      }
      return [];
    } catch (e) {
      debugPrint('NativeLogger: Failed to get all log files: $e');
      return [];
    }
  }

  /// Share the current log file
  Future<bool> shareLogFile() async {
    try {
      // Try the new shareable path method first
      String? filePath;
      if (Platform.isAndroid) {
        filePath = await getShareableLogFilePath();
        debugPrint('NativeLogger: Got shareable path: $filePath');
      } else {
        filePath = await getLogFilePath();
      }

      if (filePath != null) {
        debugPrint('NativeLogger: Attempting to share file: $filePath');

        try {
          Share.shareXFiles([XFile(filePath)], text: 'App Logs');
          debugPrint('NativeLogger: Share initiated successfully');
          return true;
        } catch (shareError) {
          debugPrint('NativeLogger: XFile share failed: $shareError');
          // Fallback: try sharing the path as text
          Share.share('Log file: $filePath', subject: 'App Logs');
          return true;
        }
      } else {
        debugPrint('NativeLogger: No log file available to share');
        return false;
      }
    } catch (e) {
      debugPrint('NativeLogger: Failed to share log file: $e');
      return false;
    }
  }

  /// Share all available log files
  Future<bool> shareAllLogFiles() async {
    try {
      final filePaths = await getAllLogFiles();
      if (filePaths.isNotEmpty) {
        final xFiles = filePaths.map((path) => XFile(path)).toList();
        // Use the non-result variant to avoid callback issues
        Share.shareXFiles(xFiles, text: 'App Logs');
        return true;
      } else {
        debugPrint('NativeLogger: No log files available to share');
        return false;
      }
    } catch (e) {
      debugPrint('NativeLogger: Failed to share all log files: $e');
      return false;
    }
  }

  // Convenience methods for common log types

  /// Log authentication events
  Future<void> logAuth(String event, {Map<String, dynamic>? details}) async {
    final message = details != null
        ? '$event\nDetails: ${_formatDetails(details)}'
        : event;
    await log('AUTH', message);
  }

  /// Log API requests with full details
  Future<void> logApiRequest({
    required String url,
    required String method,
    Map<String, String>? headers,
    String? payload,
    int? statusCode,
    String? response,
    String? error,
  }) async {
    final message = StringBuffer();
    message.writeln('Method: $method');
    message.writeln('URL: $url');

    if (headers != null && headers.isNotEmpty) {
      message.writeln('Headers: ${_formatHeaders(headers)}');
    }

    if (payload != null) {
      message.writeln('Payload: $payload');
    }

    if (statusCode != null) {
      message.writeln('Status: $statusCode');
    }

    if (response != null) {
      message.writeln('Response: $response');
    }

    if (error != null) {
      message.writeln('Error: $error');
    }

    await log('API REQUEST', message.toString().trim());
  }

  /// Log SMS events
  Future<void> logSms(String event, {Map<String, dynamic>? details}) async {
    final message = details != null
        ? '$event\nDetails: ${_formatDetails(details)}'
        : event;
    await log('SMS', message);
  }

  /// Log network connectivity changes
  Future<void> logNetwork(String event, {Map<String, dynamic>? details}) async {
    final message = details != null
        ? '$event\nDetails: ${_formatDetails(details)}'
        : event;
    await log('NETWORK', message);
  }

  /// Log service state changes
  Future<void> logService(
    String serviceName,
    String state, {
    Map<String, dynamic>? details,
  }) async {
    final message = details != null
        ? 'Service: $serviceName\nState: $state\nDetails: ${_formatDetails(details)}'
        : 'Service: $serviceName\nState: $state';
    await log('SERVICE', message);
  }

  /// Log permission events
  Future<void> logPermission(
    String event, {
    Map<String, dynamic>? details,
  }) async {
    final message = details != null
        ? '$event\nDetails: ${_formatDetails(details)}'
        : event;
    await log('PERMISSION', message);
  }

  /// Log device events
  Future<void> logDevice(String event, {Map<String, dynamic>? details}) async {
    final message = details != null
        ? '$event\nDetails: ${_formatDetails(details)}'
        : event;
    await log('DEVICE', message);
  }

  /// Log errors with stack traces
  Future<void> logError(
    String error, {
    StackTrace? stackTrace,
    Map<String, dynamic>? details,
  }) async {
    final message = StringBuffer();
    message.writeln('Error: $error');

    if (details != null) {
      message.writeln('Details: ${_formatDetails(details)}');
    }

    if (stackTrace != null) {
      message.writeln('Stack Trace:\n$stackTrace');
    }

    await log('ERROR', message.toString().trim());
  }

  // Helper methods

  String _formatDetails(Map<String, dynamic> details) {
    return details.entries.map((e) => '${e.key}: ${e.value}').join(', ');
  }

  String _formatHeaders(Map<String, String> headers) {
    return headers.entries.map((e) => '${e.key}: ${e.value}').join(', ');
  }
}
