import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/sim_provider.dart';

class PermissionProvider extends ChangeNotifier {
  static const MethodChannel _batteryOptimizationChannel = MethodChannel(
    'com.sbpay/native',
  );

  bool _smsPermissionGranted = false;
  bool _phoneStatePermissionGranted = false;
  bool _batteryOptimizationDisabled = false;
  bool _notificationPermissionGranted = false;
  bool _isLoading = false;
  String? _error;

  bool get smsPermissionGranted => _smsPermissionGranted;
  bool get phoneStatePermissionGranted => _phoneStatePermissionGranted;
  bool get batteryOptimizationDisabled => _batteryOptimizationDisabled;
  bool get notificationPermissionGranted => _notificationPermissionGranted;
  bool get isLoading => _isLoading;
  String? get error => _error;

  bool get allPermissionsGranted =>
      _smsPermissionGranted &&
      _phoneStatePermissionGranted &&
      _batteryOptimizationDisabled &&
      _notificationPermissionGranted;

  PermissionProvider() {
    _initializePermissions();
  }

  Future<void> _initializePermissions() async {
    await checkAllPermissions();
  }

  Future<void> checkAllPermissions() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      await Future.wait([
        _checkSmsPermission(),
        _checkPhoneStatePermission(),
        _checkNotificationPermission(),
        _checkBatteryOptimization(),
      ]);
    } catch (e) {
      debugPrint('Error checking permissions: $e');
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _checkSmsPermission() async {
    try {
      final status = await Permission.sms.status;
      _smsPermissionGranted = status.isGranted;
    } catch (e) {
      debugPrint('Error checking SMS permission: $e');
      _smsPermissionGranted = false;
    }
  }

  Future<void> _checkPhoneStatePermission() async {
    try {
      final status = await Permission.phone.status;
      _phoneStatePermissionGranted = status.isGranted;
    } catch (e) {
      debugPrint('Error checking phone state permission: $e');
      _phoneStatePermissionGranted = false;
    }
  }

  Future<void> _checkNotificationPermission() async {
    try {
      final status = await Permission.notification.status;
      _notificationPermissionGranted = status.isGranted;
    } catch (e) {
      debugPrint('Error checking notification permission: $e');
      _notificationPermissionGranted = false;
    }
  }

  Future<void> _checkBatteryOptimization() async {
    debugPrint('Checking battery optimization status...');
    try {
      // First immediate check
      final result1 = await _batteryOptimizationChannel.invokeMethod(
        'isIgnoringBatteryOptimizations',
      );
      _batteryOptimizationDisabled = result1 as bool? ?? false;

      // If status is still not what we expect, try again with delays
      // This handles the case where user just returned from settings
      if (!_batteryOptimizationDisabled) {
        // Wait a bit and check again
        await Future.delayed(const Duration(milliseconds: 500));
        final result2 = await _batteryOptimizationChannel.invokeMethod(
          'isIgnoringBatteryOptimizations',
        );
        _batteryOptimizationDisabled = result2 as bool? ?? false;

        // If still not updated, wait a bit more
        if (!_batteryOptimizationDisabled) {
          await Future.delayed(const Duration(milliseconds: 1000));
          final result3 = await _batteryOptimizationChannel.invokeMethod(
            'isIgnoringBatteryOptimizations',
          );
          _batteryOptimizationDisabled = result3 as bool? ?? false;
        }
      }

      debugPrint(
        'Battery optimization disabled status: $_batteryOptimizationDisabled',
      );
    } catch (e) {
      debugPrint('Error checking battery optimization: $e');
      _batteryOptimizationDisabled = false;
    }
  }

  Future<bool> requestSmsPermission() async {
    try {
      final status = await Permission.sms.request();
      _smsPermissionGranted = status.isGranted;
      notifyListeners();
      return _smsPermissionGranted;
    } catch (e) {
      debugPrint('Error requesting SMS permission: $e');
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<bool> requestPhoneStatePermission({BuildContext? context}) async {
    try {
      final status = await Permission.phone.request();
      final wasGranted = _phoneStatePermissionGranted;
      _phoneStatePermissionGranted = status.isGranted;
      notifyListeners();

      // If permission was just granted, automatically refresh SIM info
      if (!wasGranted && _phoneStatePermissionGranted && context != null) {
        // Capture context in a local variable to avoid using it across async gap
        final localContext = context;
        try {
          // Use a post-frame callback to ensure we're not using context across async gap
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (localContext.mounted) {
              final simProvider = localContext.read<SimProvider>();
              simProvider.getSimInfo();
            }
          });
        } catch (e) {
          debugPrint('Error refreshing SIM info after permission grant: $e');
        }
      }

      return _phoneStatePermissionGranted;
    } catch (e) {
      debugPrint('Error requesting phone state permission: $e');
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<bool> requestNotificationPermission() async {
    try {
      final status = await Permission.notification.request();
      _notificationPermissionGranted = status.isGranted;
      notifyListeners();
      return _notificationPermissionGranted;
    } catch (e) {
      debugPrint('Error requesting notification permission: $e');
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<bool> requestBatteryOptimizationExemption() async {
    debugPrint('Requesting battery optimization exemption...');
    try {
      await _batteryOptimizationChannel.invokeMethod(
        'requestIgnoreBatteryOptimizations',
      );

      // Wait a bit for the user to make their choice and system to update
      await Future.delayed(const Duration(seconds: 3));

      // Recheck the status after the user interaction with improved checking
      await _checkBatteryOptimization();
      notifyListeners();

      debugPrint(
        'Battery optimization exemption request completed. Status: $_batteryOptimizationDisabled',
      );
      return _batteryOptimizationDisabled;
    } catch (e) {
      debugPrint('Error requesting battery optimization exemption: $e');
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<void> openAppSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      debugPrint('Error opening app settings: $e');
    }
  }

  Future<bool> requestAllPermissions({BuildContext? context}) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final results = await Future.wait([
        requestSmsPermission(),
        requestPhoneStatePermission(context: context),
        requestNotificationPermission(),
        requestBatteryOptimizationExemption(),
      ]);

      final allGranted = results.every((granted) => granted);

      if (!allGranted) {
        _error = 'Some permissions were not granted. Please check Settings.';
      }

      return allGranted;
    } catch (e) {
      debugPrint('Error requesting all permissions: $e');
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  String getPermissionStatusText(String permissionType) {
    switch (permissionType) {
      case 'sms':
        return _smsPermissionGranted ? 'Granted' : 'Denied';
      case 'phone':
        return _phoneStatePermissionGranted ? 'Granted' : 'Denied';
      case 'notification':
        return _notificationPermissionGranted ? 'Granted' : 'Denied';
      case 'battery':
        return _batteryOptimizationDisabled ? 'Disabled' : 'Enabled';
      default:
        return 'Unknown';
    }
  }

  bool isPermissionGranted(String permissionType) {
    switch (permissionType) {
      case 'sms':
        return _smsPermissionGranted;
      case 'phone':
        return _phoneStatePermissionGranted;
      case 'notification':
        return _notificationPermissionGranted;
      case 'battery':
        return _batteryOptimizationDisabled;
      default:
        return false;
    }
  }
}
