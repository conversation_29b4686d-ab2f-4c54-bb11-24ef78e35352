import 'dart:async';

import 'package:android_package_installer/android_package_installer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:com.sbpay/core/config/api_config.dart';
import 'package:com.sbpay/services/network/api_service.dart';

/// Provider version of your AutoUpdateCubit/State
class AutoUpdateProvider extends ChangeNotifier {
  AutoUpdateProvider() {
    // Don't start checking immediately - wait for explicit initialization
    // This prevents race conditions with AuthProvider initialization
  }

  // State fields (from AutoUpdateState)
  bool isChecking = false; // Start as false, not true
  bool isDownloading = false;
  bool updateAvailable = false;
  bool updateDownloaded = false;
  double progress = 0.0;
  bool _hasInitialized = false; // Flag to prevent multiple initializations

  String? currentVersion;
  String? latestVersion;
  String? errorMessage;

  /// Initialize and check for updates - should be called after AuthProvider is ready
  Future<void> initialize() async {
    if (isChecking || _hasInitialized) {
      return; // Prevent multiple initializations
    }

    _hasInitialized = true;
    await checkForUpdate();
  }

  /// Check for updates (migrated from Cubit)
  Future<void> checkForUpdate() async {
    isChecking = true;
    errorMessage = null;
    notifyListeners();

    try {
      final apiServices = ApiService();

      // Wait for API service to be initialized with a reasonable timeout
      int retryCount = 0;
      const maxRetries = 20; // Increased from 7 to handle slower release builds
      const retryInterval = Duration(milliseconds: 250); // Reduced interval

      while (!apiServices.initialized && retryCount < maxRetries) {
        await Future.delayed(retryInterval);
        retryCount++;
      }

      if (!apiServices.initialized) {
        debugPrint(
          'AutoUpdateProvider: API service not initialized after ${maxRetries * retryInterval.inMilliseconds}ms, skipping update check',
        );
        isChecking = false;
        notifyListeners();
        return;
      }

      final response = await apiServices.checkForUpdates();
      debugPrint('AutoUpdateProvider: Update check response: $response');

      final info = await PackageInfo.fromPlatform();
      currentVersion = info.version;
      final currentBuild = info.buildNumber;
      latestVersion = response.keys.contains('latest_version')
          ? response['latest_version'] ?? '1.0.0'
          : '1.0.0';
      final latestBuild = 1;

      updateAvailable = isCurrentVersionOlder(
        currentVersion: currentVersion!,
        currentBuild: int.parse(currentBuild),
        newVersion: latestVersion!,
        newBuild: latestBuild,
      );

      debugPrint(
        'AutoUpdateProvider: Update available: $updateAvailable (current: $currentVersion, latest: $latestVersion)',
      );
    } catch (e) {
      debugPrint('AutoUpdateProvider: Error checking for updates: $e');
      errorMessage = e.toString();
    } finally {
      isChecking = false;
      notifyListeners();
    }
  }

  /// check if current version is older than available version
  bool isCurrentVersionOlder({
    required String currentVersion,
    required int currentBuild,
    required String newVersion,
    required int newBuild,
  }) {
    debugPrint('====>>$currentVersion : $newVersion');
    List<int> parseVersion(String version) =>
        version.split('.').map(int.parse).toList();

    final currentParts = parseVersion(currentVersion);
    final newParts = parseVersion(newVersion);

    for (var i = 0; i < 3; i++) {
      if (currentParts[i] < newParts[i]) return true;
      if (currentParts[i] > newParts[i]) return false;
    }

    return currentBuild < newBuild;
  }

  /// Start downloading the update
  Future<void> downloadUpdate() async {
    if (!updateAvailable || isDownloading) return;

    isDownloading = true;
    updateDownloaded = false;
    progress = 0.0;
    errorMessage = null;
    notifyListeners();

    try {
      final dir = await getTemporaryDirectory();
      final apkPath = '${dir.path}/update.apk';
      final apkUrl = '${ApiConfig.apiDomain}/api/apk-download/download/DEPOSIT';
      debugPrint('Starting download: $apkUrl');

      await ApiService().downloadApkFile(
        apkUrl,
        apkPath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            progress = received / total;
            updateDownloaded = progress >= 1.0;
            isDownloading = !updateDownloaded;
            notifyListeners();
          }
        },
      );
    } catch (e, stackTrace) {
      debugPrint('Download error: $e');
      debugPrint('StackTrace: $stackTrace');
      isDownloading = false;
      errorMessage = e.toString();
      notifyListeners();
    }
  }

  /// Cancel download
  void cancelDownload() {
    isDownloading = false;
    progress = 0.0;
    notifyListeners();
  }

  /// Reset state
  void reset() {
    isDownloading = false;
    updateDownloaded = false;
    progress = 0.0;
    errorMessage = null;
    notifyListeners();
  }

  /// Install update (simulate or hook into platform installer)
  Future<void> installUpdate() async {
    if (!updateDownloaded) return;

    try {
      final dir = await getTemporaryDirectory();
      final apkPath = '${dir.path}/update.apk';
      await AndroidPackageInstaller.installApk(apkFilePath: apkPath);

      updateAvailable = false;
      updateDownloaded = false;
      progress = 0.0;
    } catch (e) {
      errorMessage = e.toString();
    }

    notifyListeners();
  }

  /// Reset error state
  void clearError() {
    errorMessage = null;
    notifyListeners();
  }

  /// Reset update flags (e.g., after user dismisses)
  void clearUpdateFlag() {
    updateAvailable = false;
    updateDownloaded = false;
    progress = 0.0;
    notifyListeners();
  }

  @override
  void dispose() {
    reset();
    super.dispose();
  }
}
