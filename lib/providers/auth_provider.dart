import 'dart:io';

import 'package:com.sbpay/core/widgets/global_alert.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:path/path.dart';
import 'package:uuid/uuid.dart';

import '../models/user.dart';
import '../services/network/api_service.dart';
import '../services/native_logger.dart';

class AuthProvider extends ChangeNotifier {
  User? _user;
  bool _isLoading = true;
  bool _isInitialized = false;
  String? _deviceId;
  Box<User>? _authBox;
  final ApiService _apiService = ApiService();
  final _logger = NativeLogger();

  User? get user => _user;
  bool get isAuthenticated => _user != null && _user!.token.isNotEmpty;
  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;
  String? get deviceId => _deviceId;

  /// Waits for the provider to finish initializing
  Future<void> waitForInitialization() async {
    if (_isInitialized) return;
    // Wait until initialization is complete
    while (!_isInitialized && _isLoading) {
      await Future.delayed(const Duration(milliseconds: 10));
    }
  }

  AuthProvider() {
    _initialize();
  }

  Future<void> _initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('AuthProvider: Starting initialization...');
      _isLoading = true;
      notifyListeners();

      // Initialize Hive
      _authBox = await Hive.openBox<User>('auth_box');
      debugPrint('AuthProvider: Hive box opened successfully');

      // Get or create device ID
      _deviceId = await _getDeviceId();
      debugPrint('AuthProvider: Device ID obtained: $_deviceId');

      // Initialize API service
      await _apiService.initialize(deviceId: _deviceId);
      debugPrint('AuthProvider: API service initialized');

      // Load user if exists
      final storedUser = _authBox?.get('current_user');
      debugPrint(
        'AuthProvider: Loaded user from storage: ${storedUser?.email}',
      );

      // If user exists, set as authenticated (token validation will happen on first API call)
      if (storedUser?.token != null) {
        debugPrint('AuthProvider: User has token, setting as authenticated');
        _user = storedUser;

        // Initialize API service with token
        await _apiService.initialize(
          authToken: storedUser!.token,
          deviceId: _deviceId,
        );
        debugPrint('AuthProvider: API service re-initialized with token');
      } else {
        debugPrint('AuthProvider: No user token found');
      }

      _isInitialized = true;
      debugPrint('AuthProvider: Initialization completed successfully');
    } catch (e) {
      debugPrint('AuthProvider: Error during initialization: $e');
      // If there's an error during initialization, clear user data to be safe
      await clearUser();
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
      debugPrint(
        'AuthProvider: Initialization finished, isAuthenticated: $isAuthenticated',
      );
    }
  }

  Future<String> _getDeviceId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return androidInfo.id;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return iosInfo.identifierForVendor ?? const Uuid().v4();
      }

      return const Uuid().v4();
    } catch (e) {
      debugPrint('Error getting device ID: $e');
      return const Uuid().v4();
    }
  }

  Future<void> setUser(User user) async {
    try {
      _user = user;
      debugPrint('AuthProvider: Saving user to storage: ${user.email}');
      await _authBox?.put('current_user', user);
      debugPrint('AuthProvider: User saved successfully');

      // Update API service with new auth token
      await _apiService.initialize(authToken: user.token, deviceId: _deviceId);

      notifyListeners();
    } catch (e) {
      debugPrint('Error setting user: $e');
      rethrow;
    }
  }

  Future<void> clearUser() async {
    try {
      // await _apiService.logout();
      _user = null;
      await _authBox?.delete('current_user');
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing user: $e');
      rethrow;
    }
  }

  /// Clears all local storage data including all Hive boxes
  Future<void> clearAllLocalData() async {
    try {
      // Clear the auth box
      await _authBox?.clear();

      // Note: Other providers will clear their own boxes
      // This is handled in the _handleSignOut method in home_screen.dart

      debugPrint('All local data cleared successfully');
    } catch (e) {
      debugPrint('Error clearing all local data: $e');
      rethrow;
    }
  }

  /// Refreshes user data from storage
  Future<void> refreshUser() async {
    try {
      final storedUser = _authBox?.get('current_user');
      if (storedUser != null) {
        _user = storedUser;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error refreshing user: $e');
    }
  }

  Future<void> updateSimInfo(VerifiedSim simInfo) async {
    if (_user != null) {
      try {
        final updatedVerifiedSims = List<VerifiedSim>.from(_user!.verifiedSims);

        // Check if SIM already exists and update it, otherwise add new
        final existingIndex = updatedVerifiedSims.indexWhere(
          (sim) =>
              sim.subscriptionId == simInfo.subscriptionId &&
              sim.slotIndex == simInfo.slotIndex,
        );

        if (existingIndex != -1) {
          updatedVerifiedSims[existingIndex] = simInfo.copyWith(
            phoneNumber: _user!.phoneNumber,
            isVerified: true,
          );
        } else {
          updatedVerifiedSims.add(
            simInfo.copyWith(phoneNumber: _user!.phoneNumber, isVerified: true),
          );
        }

        final updatedUser = _user!.copyWith(verifiedSims: updatedVerifiedSims);
        await setUser(updatedUser);
      } catch (e) {
        debugPrint('Error updating SIM info: $e');
      }
    }
  }

  Future<Map<String, dynamic>> login(String email, String password) async {
    await _logger.logAuth(
      'AuthProvider login started',
      details: {'email': email},
    );

    try {
      _isLoading = true;
      notifyListeners();

      final response = await _apiService.login(email, password);

      if (response['success'] == true && response['data'] != null) {
        try {
          final user = User.fromJson(response['data']);
          await setUser(user);
          await _logger.logAuth(
            'AuthProvider login successful',
            details: {'email': email, 'userId': user.id},
          );
        } catch (e, stackTrace) {
          debugPrint('Error setting user: $e');
          debugPrint('Stack trace: $stackTrace');
          await _logger.logError(
            'AuthProvider user setup error: $e',
            stackTrace: stackTrace,
            details: {'email': email},
          );
          return {
            'success': false,
            'message': 'An error occurred during login',
          };
        }
        return {'success': true, 'user': user};
      }

      await _logger.logAuth(
        'AuthProvider login failed',
        details: {'email': email, 'message': response['message']},
      );
      return {'success': false, 'message': response['message']};
    } catch (e) {
      debugPrint('Login error: $e');
      await _logger.logError(
        'AuthProvider login error: $e',
        details: {'email': email},
      );
      return {'success': false, 'message': 'An error occurred during login'};
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<Map<String, dynamic>> sendOtp(
    String phoneNumber, {
    int? simSlot,
    String? countryCode,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await _apiService.sendOtp(
        phoneNumber,
        countryCode ?? '+880', // Default to Bangladesh country code
        simSlot: simSlot,
      );

      return response;
    } catch (e) {
      debugPrint('Error sending OTP: $e');
      return {'success': false, 'message': 'Failed to send OTP: $e'};
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<Map<String, dynamic>> verifyOtp(
    String phoneNumber,
    String otp, {
    int? simSlot,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await _apiService.verifyOtp(
        phoneNumber,
        otp,
        simSlot: simSlot,
      );

      if (response['success'] == true && _user != null) {
        // Note: The User model doesn't have a phoneVerified field
        // We'll just update the phone number if needed
        if (_user!.phoneNumber != phoneNumber) {
          final updatedUser = _user!.copyWith(phoneNumber: phoneNumber);
          await setUser(updatedUser);
        }
      }

      return response;
    } catch (e) {
      debugPrint('Error verifying OTP: $e');
      return {'success': false, 'message': 'Failed to verify OTP'};
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<Map<String, dynamic>> verifyPhoneNumber(
    String phoneNumber,
    String otp,
    int simSlot,
  ) async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await _apiService.verifyPhoneNumber(
        phoneNumber,
        otp,
        simSlot,
      );

      if (response['success'] == true && _user != null) {
        // Update user with verified SIM info
        // Construct simSignature consistently with how it's checked in getSimVerificationStatus
        final subscriptionId =
            int.tryParse(
              response['data']?['subscriptionId']?.toString() ?? '0',
            ) ??
            0;
        final iccId = response['data']?['iccId']?.toString() ?? '';
        final simSignature = '$subscriptionId-$iccId-$simSlot';

        final simInfo = VerifiedSim(
          phoneNumber: phoneNumber,
          subscriptionId: subscriptionId,
          slotIndex: simSlot,
          isVerified: true,
          iccId: iccId,
          simSignature: simSignature,
          verifiedAt: DateTime.now().toIso8601String(),
          carrierName: response['data']?['carrierName']?.toString() ?? '',
          simSerialNumber:
              response['data']?['simSerialNumber']?.toString() ?? '',
        );

        await updateSimInfo(simInfo);
      }

      return response;
    } catch (e) {
      debugPrint('Error verifying phone number: $e');
      return {'success': false, 'message': 'Failed to verify phone number'};
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<Map<String, dynamic>> checkForUpdates() async {
    try {
      return await _apiService.checkForUpdates();
    } catch (e) {
      debugPrint('Error checking for updates: $e');
      return {'success': false, 'message': 'Failed to check for updates'};
    }
  }

  bool validateSimInfo(VerifiedSim currentSimInfo) {
    if (_user?.verifiedSims == null || currentSimInfo.simSerialNumber == null) {
      return false;
    }

    return _user!.verifiedSims.any(
      (sim) =>
          // Primary verification using simSerialNumber
          sim.simSerialNumber == currentSimInfo.simSerialNumber &&
          // Additional checks for enhanced security
          sim.subscriptionId == currentSimInfo.subscriptionId &&
          sim.iccId == currentSimInfo.iccId &&
          sim.slotIndex == currentSimInfo.slotIndex,
    );
  }

  List<VerifiedSim> get verifiedSims => _user?.verifiedSims ?? [];

  bool isSimVerified(int subscriptionId, String iccId, int slotIndex) {
    return verifiedSims.any(
      (sim) =>
          sim.subscriptionId == subscriptionId &&
          sim.iccId == iccId &&
          sim.slotIndex == slotIndex &&
          sim.isVerified,
    );
  }

  String getSimVerificationStatus(List<dynamic> activeSimCards) {
    if (activeSimCards.isEmpty) return 'Not Verified';

    final verificationStatus = activeSimCards.map((sim) {
      // Use the same approach for constructing simSignature as in the verifyPhoneNumber method
      // but also check if simSignature from VerifiedSim matches
      final constructedSimSignature =
          '${sim['subscriptionId']}-${sim['iccId']}-${sim['slotIndex']}';

      return verifiedSims
          .where((vSim) => vSim.isVerified)
          .any(
            (verifiedSim) =>
                (verifiedSim.simSignature == constructedSimSignature ||
                (verifiedSim.subscriptionId == sim['subscriptionId'] &&
                    verifiedSim.iccId == sim['iccId'] &&
                    verifiedSim.slotIndex == sim['slotIndex'])),
          );
    }).toList();

    final allVerified = verificationStatus.every((status) => status);
    final someVerified = verificationStatus.any((status) => status);

    if (allVerified) {
      return 'Verified';
    } else if (someVerified) {
      return 'Pending';
    } else {
      return 'Not Verified';
    }
  }

  @override
  void dispose() {
    _authBox?.close();
    super.dispose();
  }
}
