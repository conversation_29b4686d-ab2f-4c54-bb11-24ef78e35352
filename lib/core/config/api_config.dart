class ApiConfig {
  // Production API
  // static const String apiDomain = 'https://api.sbkash.com';

  // Staging API
  static const String apiDomain = 'https://sbpay-api.bajiexch365.com';

  // Local API
  // static const String apiDomain = 'http://*************:7700';

  // Staging API (commented out)
  // static const String apiDomain = 'https://sbpay-api.bajiexch365.com';

  static const String ipLocationDomain = 'https://api.ipify.org?format=json';

  // OTP Configuration
  static const Map<String, String> otpConfig = {
    'clientId': 'e88a3355-fc82-49ba-9e8d-0e1f26312470',
    'clientSecret': 'DvlMMqlGy2hFv4WAqMsaAXlk72vOHBFVDDGDnjDN',
    'domain': 'myandroid.com',
    'apiUrl': 'https://hm2.cloudysign.com/api/user/otp-send',
  };

  // API Endpoints
  static const String loginEndpoint = '/api/auth/login';
  static const String verifyPhoneEndpoint = '/api/auth/verify-phone';
  static const String sendOtpEndpoint = '/api/auth/send-otp';

  /// send text file to backend
  static const String sendTextFileEndpoint = '/api/user/log-upload';
}
