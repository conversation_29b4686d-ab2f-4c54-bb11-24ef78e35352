import 'dart:async';
import 'dart:io' as io;
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import '../../models/sms_message.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _db;

  Future<Database> get db async {
    if (_db != null) return _db!;
    _db = await _initDb();
    return _db!;
  }

  Future<Database> _initDb() async {
    io.Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'sbpay_sms.db');

    return await openDatabase(
      path,
      version: 2, // Incremented version for the schema update
      onCreate: _onCreate,
      onUpgrade: (db, oldVersion, newVersion) async {
        if (oldVersion < 2) {
          // Add created_at column if it doesn't exist
          await db.execute('''
            ALTER TABLE pending_sms_messages 
            ADD COLUMN created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000)
          ''');
          
          // Create index on created_at
          await db.execute('''
            CREATE INDEX IF NOT EXISTS idx_pending_sms_created_at 
            ON pending_sms_messages(created_at);
          ''');
          
          debugPrint('Database upgraded to version 2: Added created_at column');
        }
      },
    );
  }

  void _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE pending_sms_messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        message_id TEXT UNIQUE,
        sender TEXT,
        body TEXT,
        timestamp INTEGER,
        created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
        sim_slot INTEGER,
        subscription_id INTEGER,
        retry_count INTEGER DEFAULT 0,
        last_retry_timestamp INTEGER DEFAULT 0,
        error TEXT
      )
    ''');
    
    // Create an index on created_at for better performance
    await db.execute('''
      CREATE INDEX idx_pending_sms_created_at ON pending_sms_messages(created_at);
    ''');
  }

  Future<int> insertPendingMessage(PendingSmsMessage message) async {
    final dbClient = await db;
    final now = DateTime.now().millisecondsSinceEpoch;
    final result = await dbClient.insert('pending_sms_messages', {
      'message_id': message.messageId,
      'sender': message.sender,
      'body': message.body,
      'timestamp': message.receivedAt.millisecondsSinceEpoch,
      'created_at': now, // Explicitly set created_at to current time
      'sim_slot': message.simSlot,
      'subscription_id': message.subscriptionId,
      'retry_count': message.retryCount,
      'last_retry_timestamp': message.lastRetryAt?.millisecondsSinceEpoch ?? 0,
      'error': message.lastError,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
    
    debugPrint('Inserted pending message: ${message.messageId} at ${DateTime.fromMillisecondsSinceEpoch(now).toIso8601String()}');
    return result;
  }

  Future<List<PendingSmsMessage>> getMessagesForRetry() async {
    final dbClient = await db;
    // Get messages with less than 300 retries and last retry more than 15 seconds ago
    final threshold = DateTime.now().millisecondsSinceEpoch - 15000;
    final List<Map<String, dynamic>> maps = await dbClient.rawQuery(
      '''
      SELECT * FROM pending_sms_messages
      WHERE retry_count < 300
        AND (last_retry_timestamp = 0 OR last_retry_timestamp <= ?)
      ORDER BY created_at ASC, id ASC
    ''',
      [threshold],
    );
    
    // Log the order of messages being returned for debugging
    if (maps.isNotEmpty) {
      debugPrint('Returning ${maps.length} messages in order:');
      for (var i = 0; i < maps.length; i++) {
        final msg = maps[i];
        debugPrint('  ${i + 1}. ID: ${msg['id']}, Created: ${DateTime.fromMillisecondsSinceEpoch(msg['created_at'] ?? msg['timestamp']).toIso8601String()}, Sender: ${msg['sender']}');
      }
    }

    return List.generate(maps.length, (i) {
      return PendingSmsMessage(
        messageId: maps[i]['message_id'],
        sender: maps[i]['sender'],
        body: maps[i]['body'],
        receivedAt: DateTime.fromMillisecondsSinceEpoch(maps[i]['timestamp']),
        simSlot: maps[i]['sim_slot'],
        subscriptionId: maps[i]['subscription_id'],
        retryCount: maps[i]['retry_count'],
        lastRetryAt: maps[i]['last_retry_timestamp'] != 0
            ? DateTime.fromMillisecondsSinceEpoch(
                maps[i]['last_retry_timestamp'],
              )
            : null,
        lastError: maps[i]['error'],
      );
    });
  }

  Future<int> updatePendingMessage(PendingSmsMessage message) async {
    final dbClient = await db;
    return await dbClient.update(
      'pending_sms_messages',
      {
        'retry_count': message.retryCount,
        'last_retry_timestamp':
            message.lastRetryAt?.millisecondsSinceEpoch ?? 0,
        'error': message.lastError,
      },
      where: 'message_id = ?',
      whereArgs: [message.messageId],
    );
  }

  Future<int> deletePendingMessage(String messageId) async {
    final dbClient = await db;
    return await dbClient.delete(
      'pending_sms_messages',
      where: 'message_id = ?',
      whereArgs: [messageId],
    );
  }

  Future<int> clearPendingMessages() async {
    final dbClient = await db;
    return await dbClient.delete('pending_sms_messages');
  }

  Future<int> getPendingMessageCount() async {
    final dbClient = await db;
    return Sqflite.firstIntValue(
          await dbClient.rawQuery('SELECT COUNT(*) FROM pending_sms_messages'),
        ) ??
        0;
  }

  /// Completely clears all data from the database
  Future<void> clearAllData() async {
    final dbClient = await db;
    await dbClient.delete('pending_sms_messages');
    // If we add more tables in the future, we would clear them here as well
    debugPrint('Database cleared successfully');
  }

  Future<void> close() async {
    final dbClient = await db;
    dbClient.close();
  }
}
