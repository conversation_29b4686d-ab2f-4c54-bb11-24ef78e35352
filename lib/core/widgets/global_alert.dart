import 'package:flash/flash.dart';
import 'package:flash/flash_helper.dart';
import 'package:flutter/material.dart';

enum AlertType { success, error, warning, info }

class GlobalAlert {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  static void show({
    required String title,
    required String message,
    AlertType type = AlertType.info,
    int duration = 3000,
  }) {
    final context = navigatorKey.currentContext;
    if (context == null) return;

    context.showFlash<void>(
      duration: Duration(milliseconds: duration),
      builder: (context, controller) {
        return FlashBar(
          controller: controller,
          position: FlashPosition.top,
          behavior: FlashBehavior.floating,
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          backgroundColor: Colors.white,
          icon: Icon(_getIconForType(type), color: _getColorForType(type)),
          shouldIconPulse: false,
          title: Text(
            title,
            style: TextStyle(
              color: _getColorForType(type),
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          content: Text(
            message,
            style: const TextStyle(color: Colors.black87, fontSize: 14),
          ),
          forwardAnimationCurve: Curves.easeOutBack,
        );
      },
    );
  }

  static IconData _getIconForType(AlertType type) {
    switch (type) {
      case AlertType.success:
        return Icons.check_circle;
      case AlertType.error:
        return Icons.error;
      case AlertType.warning:
        return Icons.warning;
      case AlertType.info:
        return Icons.info;
    }
  }

  static Color _getColorForType(AlertType type) {
    switch (type) {
      case AlertType.success:
        return Colors.green;
      case AlertType.error:
        return Colors.red;
      case AlertType.warning:
        return Colors.orange;
      case AlertType.info:
        return Colors.blue;
    }
  }
}
