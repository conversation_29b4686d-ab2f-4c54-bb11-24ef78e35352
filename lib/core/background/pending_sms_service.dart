import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../providers/sms_provider.dart';

class PendingSmsService {
  static final PendingSmsService _instance = PendingSmsService._internal();
  factory PendingSmsService() => _instance;
  PendingSmsService._internal();

  Timer? _retryTimer;
  SmsProvider? _smsProvider;

  /// Initialize the service with the SMS provider
  void initialize(SmsProvider smsProvider) {
    _smsProvider = smsProvider;
    
    // Start periodic checking of pending messages
    _startPeriodicRetry();
    
    debugPrint('Pending SMS service initialized');
  }

  /// Start periodic checking of pending messages every 30 seconds
  void _startPeriodicRetry() {
    // if user is not logged in, don't start the timer
    if (_smsProvider == null) {
      return;
    }
    // Cancel any existing timer
    _stopPeriodicRetry();
    
    // Start a periodic timer to check for pending messages
    _retryTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      _checkAndRetryPendingMessages();
    });
    
    debugPrint('Periodic pending SMS retry started (every 30 seconds)');
  }

  /// Stop periodic checking
  void _stopPeriodicRetry() {
    _retryTimer?.cancel();
    _retryTimer = null;
  }

  /// Check for pending messages and retry sending them
  Future<void> _checkAndRetryPendingMessages() async {
    // Only proceed if we have an SMS provider
    if (_smsProvider == null) {
      return;
    }
    
    // Check if we have internet access using the SmsProvider's connectivity service
    // We'll do a fresh check rather than relying on the cached value
    final hasInternetAccess = await _smsProvider!.connectivityService.checkInternetAccess();
    
    if (hasInternetAccess) {
      debugPrint('Internet access confirmed, checking for pending messages');
      
      // Retry pending messages
      await _smsProvider!.retryPendingMessages();
    } else {
      debugPrint('No internet access, skipping pending message retry');
    }
  }

  /// Force immediate retry of pending messages
  Future<void> forceRetryPendingMessages() async {
    if (_smsProvider != null) {
      await _smsProvider!.retryPendingMessages();
    }
  }

  /// Dispose of the service
  void dispose() {
    _stopPeriodicRetry();
    debugPrint('Pending SMS service disposed');
  }
}
