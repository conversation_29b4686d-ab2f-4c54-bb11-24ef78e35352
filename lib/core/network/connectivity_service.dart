import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../../services/native_logger.dart';

class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  bool _isConnected = false;
  bool _hasInternetAccess = false;
  Timer? _internetCheckTimer;
  final List<Function> _listeners = [];
  final _logger = NativeLogger();

  bool get isConnected => _isConnected;
  bool get hasInternetAccess => _hasInternetAccess;

  /// Public method to check internet access
  Future<bool> checkInternetAccess() async {
    return await _checkInternetAccess();
  }

  Future<void> initialize() async {
    // Get initial connectivity status
    final results = await _connectivity.checkConnectivity();
    _isConnected = _areResultsConnected(results);

    // Check initial internet access
    _hasInternetAccess = await _checkInternetAccess();

    // Listen for connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) {
      // Check if any of the results indicate connectivity
      final hasConnection = _areResultsConnected(results);
      final wasConnected = _isConnected;
      _isConnected = hasConnection;

      // Log connectivity changes
      _logger.logNetwork(
        'Connectivity changed',
        details: {
          'wasConnected': wasConnected,
          'isConnected': _isConnected,
          'results': results.map((r) => r.name).join(', '),
        },
      );

      // If we just reconnected, start checking for internet access
      if (!wasConnected && _isConnected) {
        _startInternetAccessCheck();
      } else if (!hasConnection) {
        // If we lost connection, stop checking and update status
        _stopInternetAccessCheck();
        final hadInternetAccess = _hasInternetAccess;
        _hasInternetAccess = false;

        // Only notify if we had internet access before
        if (hadInternetAccess) {
          _notifyListeners();
        }
      }
    });

    // Start periodic internet access check if we're connected
    if (_isConnected) {
      _startInternetAccessCheck();
    }
  }

  bool _areResultsConnected(List<ConnectivityResult> results) {
    return results.any((result) => result != ConnectivityResult.none);
  }

  Future<bool> _checkInternetAccess() async {
    try {
      // Try to connect to a reliable server
      final result = await InternetAddress.lookup(
        'google.com',
      ).timeout(const Duration(seconds: 5));

      // Check if we got a result
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      // No internet access
      return false;
    } on TimeoutException catch (_) {
      // Connection timed out, likely no internet
      return false;
    } catch (e) {
      // Other error, assume no internet
      return false;
    }
  }

  void _startInternetAccessCheck() {
    // Stop any existing timer
    _stopInternetAccessCheck();

    // Start a periodic check for internet access
    _internetCheckTimer = Timer.periodic(const Duration(seconds: 10), (
      _,
    ) async {
      final hadInternetAccess = _hasInternetAccess;
      _hasInternetAccess = await _checkInternetAccess();

      // Log internet access changes
      if (hadInternetAccess != _hasInternetAccess) {
        _logger.logNetwork(
          'Internet access changed',
          details: {
            'hadInternetAccess': hadInternetAccess,
            'hasInternetAccess': _hasInternetAccess,
          },
        );
      }

      // If we just gained internet access, notify listeners
      if (!hadInternetAccess && _hasInternetAccess) {
        _notifyListeners();
      }
    });
  }

  void _stopInternetAccessCheck() {
    _internetCheckTimer?.cancel();
    _internetCheckTimer = null;
  }

  void addListener(Function listener) {
    _listeners.add(listener);
  }

  void removeListener(Function listener) {
    _listeners.remove(listener);
  }

  void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  Future<void> dispose() async {
    await _connectivitySubscription?.cancel();
    _stopInternetAccessCheck();
  }
}
