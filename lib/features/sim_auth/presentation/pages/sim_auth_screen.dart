import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../providers/sim_provider.dart';
import '../../../../providers/auth_provider.dart';
import '../../../../models/sim_card.dart';

class SimAuthScreen extends StatefulWidget {
  const SimAuthScreen({super.key});

  @override
  State<SimAuthScreen> createState() => _SimAuthScreenState();
}

class _SimAuthScreenState extends State<SimAuthScreen> {
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshSimInfo();
    });
  }

  Future<void> _refreshSimInfo() async {
    final simProvider = context.read<SimProvider>();

    // Load SIM info
    await simProvider.getSimInfo();
  }

  Future<void> _handleSimSelection(SimCardInfo simCard) async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Navigate to phone verification screen
      if (mounted) {
        // Create a SimSlot with the actual SIM card data
        final selectedSlot = _createSimSlotFromCard(simCard);
        Navigator.of(
          context,
        ).pushNamed('/phone_auth', arguments: {'selectedSlot': selectedSlot});
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Helper method to create a SimSlot with SIM card data
  dynamic _createSimSlotFromCard(SimCardInfo simCard) {
    return {
      'slotIndex': simCard.slotIndex,
      'subscriptionId': simCard.subscriptionId,
      'iccId': simCard.iccId,
      'displayName': simCard.displayName,
      'carrierName': simCard.carrierName,
      'simSerialNumber': simCard.iccId, // Using iccId as serial number
      'isAvailable': true,
      'isVerified': false, // Will be checked on PhoneAuthScreen
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('SIM Authentication')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Select SIM Card to Verify',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Choose which SIM card you want to verify for SMS monitoring',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            if (_error != null) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error, color: Colors.red.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _error!,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            Expanded(
              child: Consumer2<SimProvider, AuthProvider>(
                builder: (context, simProvider, authProvider, _) {
                  if (simProvider.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (simProvider.error != null) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: Colors.red.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading SIM cards',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            simProvider.error!,
                            style: TextStyle(color: Colors.grey[600]),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton(
                            onPressed: _refreshSimInfo,
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }

                  final activeSimCards = simProvider.activeSimCards;
                  final phoneCount =
                      simProvider.simInfo?.phoneCount ??
                      2; // Default to 2 slots

                  // Create all slots like React Native
                  final allSlots = List.generate(phoneCount, (index) {
                    // Find active SIM for this slot
                    final activeSim = activeSimCards.firstWhere(
                      (sim) => sim.slotIndex == index,
                      orElse: () => SimCardInfo(
                        subscriptionId: -1,
                        slotIndex: index,
                        iccId: '',
                        displayName: '',
                        carrierName: '',
                        countryIso: '',
                        isEmbedded: false,
                        phoneNumber: '',
                      ),
                    );

                    // Check if this SIM is verified (only if it's a real SIM, not the dummy)
                    final isVerified = activeSim.subscriptionId != -1
                        ? authProvider.isSimVerified(
                            activeSim.subscriptionId,
                            activeSim.iccId,
                            activeSim.slotIndex,
                          )
                        : false;

                    return {
                          'slotIndex': index,
                          'isAvailable': activeSim.subscriptionId != -1,
                          'simCard': activeSim.subscriptionId != -1
                              ? activeSim
                              : null,
                          'isVerified': isVerified,
                        }
                        as Map<String, dynamic>;
                  });

                  return ListView.builder(
                    itemCount: allSlots.length,
                    itemBuilder: (context, index) {
                      final slot = allSlots[index];
                      final isAvailable = slot['isAvailable'] as bool;
                      final isVerified = slot['isVerified'] as bool;
                      final simCard = slot['simCard'] as SimCardInfo?;

                      return Card(
                        margin: const EdgeInsets.only(bottom: 12),
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: isVerified
                                ? Colors.green
                                : isAvailable
                                ? Colors.blue
                                : Colors.grey,
                            child: Icon(
                              isVerified
                                  ? Icons.check
                                  : isAvailable
                                  ? Icons.sim_card
                                  : Icons.sim_card_alert,
                              color: Colors.white,
                            ),
                          ),
                          title: Text(
                            'SIM Slot ${index + 1}',
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (isAvailable && simCard != null) ...[
                                // Text('Carrier: ${simCard.carrierName}'),
                                // Text('Display: ${simCard.displayName}'),
                                // Text('Country: ${simCard.countryIso}'),
                              ] else ...[
                                Text(
                                  'No SIM card inserted',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ],
                              if (isVerified)
                                Container(
                                  margin: const EdgeInsets.only(top: 4),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.green.shade50,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    'Verified',
                                    style: TextStyle(
                                      color: Colors.green.shade700,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          trailing: isVerified
                              ? Icon(Icons.check_circle, color: Colors.green)
                              : isAvailable
                              ? Icon(Icons.arrow_forward_ios, size: 16)
                              : Icon(Icons.block, color: Colors.grey),
                          onTap: (_isLoading || isVerified || !isAvailable)
                              ? null
                              : () => _handleSimSelection(simCard!),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
