// import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';
// import 'package:country_code_picker/country_code_picker.dart';
// import '../../../../providers/auth_provider.dart';
// import '../../../../models/sim_card.dart';

// class PhoneAuthScreen extends StatefulWidget {
//   final SimSlot selectedSlot;

//   const PhoneAuthScreen({super.key, required this.selectedSlot});

//   @override
//   State<PhoneAuthScreen> createState() => _PhoneAuthScreenState();
// }

// class _PhoneAuthScreenState extends State<PhoneAuthScreen> {
//   final _formKey = GlobalKey<FormState>();
//   final _phoneController = TextEditingController();
//   final _otpController = TextEditingController();
//   bool _isLoading = false;
//   bool _otpSent = false;
//   String? _error;
//   String _selectedCountryCode = '+91'; // Default to India

//   @override
//   void dispose() {
//     _phoneController.dispose();
//     _otpController.dispose();
//     super.dispose();
//   }

//   Future<void> _sendOtp() async {
//     if (!_formKey.currentState!.validate()) return;

//     setState(() {
//       _isLoading = true;
//       _error = null;
//     });

//     try {
//       final authProvider = context.read<AuthProvider>();
//       final phoneNumber = _phoneController.text.trim();

//       final response = await authProvider.sendOtp(
//         phoneNumber,
//         simSlot: widget.selectedSlot.value,
//         countryCode: _selectedCountryCode,
//       );

//       if (response['success'] == true) {
//         setState(() {
//           _otpSent = true;
//         });

//         if (mounted) {
//              ScaffoldMessenger.of(context)
          // ..clearSnackBars()
          // ..showSnackBar(
//             SnackBar(
//               content: Text(response['message'] ?? 'OTP sent successfully!'),
//               backgroundColor: Colors.green,
//             ),
//           );
//         }
//       } else {
//         throw Exception(
//           response['message'] ?? 'Failed to send OTP. Please try again.',
//         );
//       }
//     } catch (e) {
//       setState(() {
//         _error = e.toString().replaceAll('Exception: ', '');
//       });

//       if (mounted) {
//            ScaffoldMessenger.of(context)
          // ..clearSnackBars()
          // ..showSnackBar(
//           SnackBar(
//             content: Text(
//               'Error: ${e.toString().replaceAll('Exception: ', '')}',
//             ),
//             backgroundColor: Colors.red,
//           ),
//         );
//       }
//     } finally {
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     }
//   }

//   Future<void> _verifyOtp() async {
//     if (!_formKey.currentState!.validate()) return;

//     setState(() {
//       _isLoading = true;
//       _error = null;
//     });

//     try {
//       final authProvider = context.read<AuthProvider>();
//       final phoneNumber = _phoneController.text.trim();
//       final otp = _otpController.text.trim();

//       final response = await authProvider.verifyOtp(
//         '$_selectedCountryCode${phoneNumber.replaceAll(RegExp(r'\D'), '')}',
//         otp,
//         simSlot: widget.selectedSlot.value,
//       );

//       if (response['success'] == true) {
//         if (mounted) {
//           Navigator.of(context).pop(true); // Return success
//         }
//       } else {
//         throw Exception(
//           response['message'] ?? 'Failed to verify OTP. Please try again.',
//         );
//       }
//     } catch (e) {
//       setState(() {
//         _error = e.toString().replaceAll('Exception: ', '');
//       });

//       if (mounted) {
//            ScaffoldMessenger.of(context)
          // ..clearSnackBars()
          // ..showSnackBar(
//           SnackBar(
//             content: Text(
//               'Error: ${e.toString().replaceAll('Exception: ', '')}',
//             ),
//             backgroundColor: Colors.red,
//           ),
//         );
//       }
//     } finally {
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(title: const Text('Phone Verification')),
//       body: SingleChildScrollView(
//         padding: const EdgeInsets.all(16.0),
//         child: Form(
//           key: _formKey,
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.stretch,
//             children: [
//               // SIM Slot Info
//               Card(
//                 child: Padding(
//                   padding: const EdgeInsets.all(16.0),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       const Text(
//                         'Verifying SIM Card',
//                         style: TextStyle(
//                           fontSize: 18,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                       const SizedBox(height: 8),
//                       Text('Slot: ${widget.selectedSlot.displayName}'),
//                     ],
//                   ),
//                 ),
//               ),
//               const SizedBox(height: 24),

//               // Phone Number Field with Country Code Picker
//               const Text(
//                 'Phone Number',
//                 style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
//               ),
//               const SizedBox(height: 8),
//               Row(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   // Country Code Picker
//                   Container(
//                     decoration: BoxDecoration(
//                       border: Border.all(color: Colors.grey),
//                       borderRadius: BorderRadius.circular(4.0),
//                     ),
//                     child: CountryCodePicker(
//                       onChanged: (country) {
//                         setState(() {
//                           _selectedCountryCode = country.dialCode ?? '+91';
//                         });
//                       },
//                       initialSelection: 'IN',
//                       favorite: const ['+91', 'IN'],
//                       showCountryOnly: false,
//                       showOnlyCountryWhenClosed: false,
//                       alignLeft: false,
//                       showFlag: true,
//                       showFlagDialog: true,
//                       padding: EdgeInsets.zero,
//                       textStyle: const TextStyle(
//                         color: Colors.black,
//                         fontSize: 16,
//                       ),
//                     ),
//                   ),
//                   const SizedBox(width: 8),
//                   // Phone Number Field
//                   Expanded(
//                     child: TextFormField(
//                       controller: _phoneController,
//                       keyboardType: TextInputType.phone,
//                       enabled: !_otpSent,
//                       decoration: const InputDecoration(
//                         hintText: 'Enter phone number',
//                         border: OutlineInputBorder(),
//                       ),
//                       validator: (value) {
//                         if (value == null || value.isEmpty) {
//                           return 'Please enter your phone number';
//                         }
//                         // Remove any non-digit characters for validation
//                         final digitsOnly = value.replaceAll(RegExp(r'\D'), '');
//                         if (digitsOnly.length < 10) {
//                           return 'Please enter a valid phone number';
//                         }
//                         return null;
//                       },
//                       onTapOutside: (event) {
//                         FocusScope.of(context).unfocus();
//                       },
//                     ),
//                   ),
//                 ],
//               ),

//               if (_otpSent) ...[
//                 const SizedBox(height: 24),
//                 TextFormField(
//                   controller: _otpController,
//                   keyboardType: TextInputType.number,
//                   decoration: const InputDecoration(
//                     labelText: 'Enter OTP',
//                     border: OutlineInputBorder(),
//                   ),
//                   validator: (value) {
//                     if (value == null || value.isEmpty) {
//                       return 'Please enter the OTP';
//                     }
//                     if (value.length < 4) {
//                       return 'OTP must be at least 4 digits';
//                     }
//                     return null;
//                   },
//                   onTapOutside: (event) {
//                     FocusScope.of(context).unfocus();
//                   },
//                 ),
//               ],

//               const SizedBox(height: 24),

//               if (_error != null) ...[
//                 Text(
//                   _error!,
//                   style: const TextStyle(color: Colors.red),
//                   textAlign: TextAlign.center,
//                 ),
//                 const SizedBox(height: 16),
//               ],

//               ElevatedButton(
//                 onPressed: _isLoading
//                     ? null
//                     : (_otpSent ? _verifyOtp : _sendOtp),
//                 child: _isLoading
//                     ? const CircularProgressIndicator()
//                     : Text(_otpSent ? 'Verify OTP' : 'Send OTP'),
//               ),

//               if (_otpSent) ...[
//                 const SizedBox(height: 16),
//                 TextButton(
//                   onPressed: _isLoading ? null : _sendOtp,
//                   child: const Text('Resend OTP'),
//                 ),
//               ],
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
