import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../providers/permission_provider.dart';
import '../../../../core/constants/app_constants.dart';

class PermissionCard extends StatelessWidget {
  const PermissionCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.security, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Permissions',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Consumer<PermissionProvider>(
              builder: (context, permissionProvider, _) {
                return Column(
                  children: [
                    _buildPermissionItem(
                      context,
                      'SMS Access',
                      'Allow the app to read and receive SMS messages for monitoring your transactions',
                      'sms',
                      permissionProvider.smsPermissionGranted,
                      permissionProvider.requestSmsPermission,
                    ),
                    const SizedBox(height: 12),
                    _buildPermissionItem(
                      context,
                      'Phone State',
                      'Allow the app to detect SIM cards and changes',
                      'phone',
                      permissionProvider.phoneStatePermissionGranted,
                      () => permissionProvider.requestPhoneStatePermission(
                        context: context,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildPermissionItem(
                      context,
                      'Notifications',
                      'Allow the app to send notifications',
                      'notification',
                      permissionProvider.notificationPermissionGranted,
                      permissionProvider.requestNotificationPermission,
                    ),
                    const SizedBox(height: 12),
                    _buildPermissionItem(
                      context,
                      'Battery Optimization',
                      'Allow the app to run in the background to ensure continuous and reliable transaction monitoring',
                      'battery',
                      permissionProvider.batteryOptimizationDisabled,
                      permissionProvider.requestBatteryOptimizationExemption,
                      isBatteryOptimization: true,
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionItem(
    BuildContext context,
    String title,
    String description,
    String permissionType,
    bool isGranted,
    Future<bool> Function() requestPermission, {
    bool isBatteryOptimization = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isGranted ? Colors.green.shade200 : Colors.red.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              _buildPermissionStatus(isGranted, isBatteryOptimization),
            ],
          ),
          if (!isGranted) ...[
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  try {
                    await requestPermission();
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context)
                        ..clearSnackBars()
                        ..showSnackBar(
                          SnackBar(
                            content: Text('Error requesting permission: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
                child: Text(
                  isBatteryOptimization ? 'Open Settings' : 'Grant Permission',
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPermissionStatus(bool isGranted, bool isBatteryOptimization) {
    final statusText = isBatteryOptimization
        ? (isGranted ? 'Disabled' : 'Enabled')
        : (isGranted ? 'Granted' : 'Denied');

    final statusColor = isGranted ? Colors.green : Colors.red;
    final backgroundColor = isGranted
        ? Colors.green.shade50
        : Colors.red.shade50;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: statusColor,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }
}
