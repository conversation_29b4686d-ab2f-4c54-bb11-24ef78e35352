import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../providers/sim_provider.dart';
import '../../../../providers/auth_provider.dart';
import '../../../../core/constants/app_constants.dart';

class SimStatusCard extends StatelessWidget {
  const SimStatusCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.sim_card, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'SIM Card Status',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Consumer2<SimProvider, AuthProvider>(
              builder: (context, simProvider, authProvider, _) {
                if (simProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (simProvider.error != null) {
                  return Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.error, color: Colors.red.shade700),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Error: ${simProvider.error}',
                            style: TextStyle(color: Colors.red.shade700),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                final simInfo = simProvider.simInfo;
                final activeSimCards = simProvider.activeSimCards;

                if (simInfo == null || activeSimCards.isEmpty) {
                  return Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.orange.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange.shade700),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            'No active SIM cards detected',
                            style: TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                final verificationStatus = authProvider
                    .getSimVerificationStatus(
                      activeSimCards.map((sim) => sim.toJson()).toList(),
                    );

                print('====>>${activeSimCards} : $verificationStatus');

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Overall status
                    GestureDetector(
                      onTap:
                          verificationStatus != AppConstants.simStatusVerified
                          ? () => Navigator.of(context).pushNamed('/sim_auth')
                          : null,
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _getStatusColor(
                            verificationStatus,
                          ).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _getStatusColor(
                              verificationStatus,
                            ).withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              _getStatusIcon(verificationStatus),
                              color: _getStatusColor(verificationStatus),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Verification Status: $verificationStatus',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: _getStatusColor(
                                        verificationStatus,
                                      ),
                                    ),
                                  ),
                                  Text(
                                    '${activeSimCards.length} active SIM card(s) detected',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 14,
                                    ),
                                  ),
                                  if (verificationStatus !=
                                      AppConstants.simStatusVerified)
                                    Text(
                                      'Tap to verify SIM cards',
                                      style: TextStyle(
                                        color: _getStatusColor(
                                          verificationStatus,
                                        ),
                                        fontSize: 12,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            if (verificationStatus !=
                                AppConstants.simStatusVerified)
                              Icon(
                                Icons.arrow_forward_ios,
                                size: 16,
                                color: _getStatusColor(verificationStatus),
                              ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Individual SIM cards
                    ...activeSimCards.map(
                      (sim) => _buildSimCard(context, sim, authProvider),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimCard(
    BuildContext context,
    dynamic sim,
    AuthProvider authProvider,
  ) {
    final isVerified = authProvider.isSimVerified(
      sim.subscriptionId,
      sim.iccId,
      sim.slotIndex,
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isVerified ? Colors.green.shade200 : Colors.orange.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'SIM ${sim.slotIndex + 1}',
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isVerified
                      ? Colors.green.shade50
                      : Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  isVerified ? 'Verified' : 'Not Verified',
                  style: TextStyle(
                    color: isVerified
                        ? Colors.green.shade700
                        : Colors.orange.shade700,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          // const SizedBox(height: 8),
          // _buildSimDetail('Carrier', sim.carrierName ?? 'Unknown'),
          // _buildSimDetail('Display Name', sim.displayName ?? 'Unknown'),
          // _buildSimDetail('Country', sim.countryIso ?? 'Unknown'),
          // _buildSimDetail('Subscription ID', sim.subscriptionId.toString()),
        ],
      ),
    );
  }

  // Widget _buildSimDetail(String label, String value) {
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(vertical: 2),
  //     child: Row(
  //       children: [
  //         SizedBox(
  //           width: 120,
  //           child: Text(
  //             '$label:',
  //             style: TextStyle(color: Colors.grey[600], fontSize: 14),
  //           ),
  //         ),
  //         Expanded(
  //           child: Text(
  //             value,
  //             style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.simStatusVerified:
        return Colors.green;
      case AppConstants.simStatusPending:
        return Colors.orange;
      case AppConstants.simStatusNotVerified:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case AppConstants.simStatusVerified:
        return Icons.check_circle;
      case AppConstants.simStatusPending:
        return Icons.pending;
      case AppConstants.simStatusNotVerified:
        return Icons.error;
      default:
        return Icons.help;
    }
  }
}
